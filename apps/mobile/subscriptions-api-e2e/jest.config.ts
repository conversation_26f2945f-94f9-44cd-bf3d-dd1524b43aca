 
export default {
  displayName: 'subscriptions-api-e2e',
  preset: '../../../jest.preset.js',
  testEnvironment: 'node',
  transform: {
    '^.+\\.[tj]s$': [
      'ts-jest',
      {
        tsconfig: '<rootDir>/tsconfig.spec.json',
      },
    ],
  },
  moduleFileExtensions: ['ts', 'js', 'html'],
  setupFiles: ['<rootDir>/jest.env.ts'],
  coverageDirectory: '../../../coverage/subscriptions-api-e2e',
};
