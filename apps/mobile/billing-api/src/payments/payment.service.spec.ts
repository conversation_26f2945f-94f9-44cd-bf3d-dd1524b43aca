import { AxiosResponse } from 'axios';
import {
  CapturePaymentRequest,
  CreateIntentResponse,
  CreatePaymentRequest,
  CreateRegisteredUserPaymentResponse,
  StripeAmountTooLargeException,
  StripeCustomerService,
  StripePaymentIntentService,
  StripeSetupIntentService,
} from '@experience/shared/nest/stripe';
import {
  CreateCustomerErrors,
  PaymentIntentErrors,
  SetupIntentErrors,
} from './payment.errors';
import {
  ExtendedUserInfoResponseDto,
  UsersApi,
} from '@experience/driver-account-api/api-client';
import { ITokenAuthUser } from '@experience/mobile/nest/authorisation';
import { Logger } from '@nestjs/common';
import { MockRepository, createMockRepository } from '../test.utils';
import { PaymentService } from './payment.service';
import { PodadminSequelizeModule } from '@experience/shared/sequelize/podadmin';
import { Test } from '@nestjs/testing';
import { jest } from '@jest/globals';
import { mockDeep } from 'jest-mock-extended';

jest.mock('@experience/shared/nest/stripe');

describe('PaymentService', () => {
  let service: PaymentService;
  let stripeSetupIntentService: StripeSetupIntentService;
  let stripeCustomerService: StripeCustomerService;
  let stripePaymentIntentService: StripePaymentIntentService;
  let usersApi: UsersApi;
  const customerId = '1s133423r5221';
  const metadata = {
    origin: 'billing-api',
    product: 'PAYG Top Up',
  };
  const ephemeralKey = '12345';
  const setupIntent = 'xxxxx';
  const logger = mockDeep<Logger>();
  const email = '<EMAIL>';
  const uid = 'uuid';
  const userId = '123';
  const userIdAsNumber = parseInt(userId);
  const user: ITokenAuthUser = {
    email,
    uuid: uid,
  };
  const setupIntentResponse: CreateIntentResponse = {
    customer: customerId,
    ephemeralKey,
    setupIntent,
  };
  const paymentIntent = 'sdfdsfdsf';
  const paymentIntentResponse: CreateRegisteredUserPaymentResponse = {
    customer: customerId,
    ephemeralKey,
    paymentIntent: paymentIntent,
  };
  const PAYMENT_REQUEST: CreatePaymentRequest = {
    amount: 80,
    currency: 'gbp',
  };
  const driverUser: ExtendedUserInfoResponseDto = {
    email,
    emailVerified: true,
    first_name: 'Mobile',
    last_name: 'Non-Tester',
    locale: 'en',
    uid: '5357be96-1495-4951-8046-c2d59ba76c33',
    status: 'active',
    deletedAtTimestamp: null,
    accountCreationTimestamp: '2023-08-24T10:14:52.000Z',
    lastSignInTimestamp: null,
    paymentProcessorId: null,
    balance: {
      currency: 'GBP',
      amount: 472,
    },
    preferences: {
      unitOfDistance: 'string',
    },
    rewards: null,
  };
  const driverUserWithStripeCard = Object.assign({}, driverUser, {
    paymentProcessorId: customerId,
  });
  let billingAccountsRepository: MockRepository;
  let billingEventsRepository: MockRepository;
  let usersRepository: MockRepository;
  const paymentMethodOptions = {
    card: {
      request_three_d_secure: 'challenge',
    },
  };
  const paymentIntentId = 'pi_12345';

  beforeAll(async () => {
    const module = await Test.createTestingModule({
      imports: [PodadminSequelizeModule],
      providers: [
        StripeSetupIntentService,
        StripeCustomerService,
        StripePaymentIntentService,
        PaymentService,
        {
          provide: UsersApi,
          useValue: new UsersApi(),
        },
        {
          provide: 'BILLING_ACCOUNTS_REPOSITORY',
          useValue: createMockRepository(),
        },
        {
          provide: 'BILLING_EVENTS_REPOSITORY',
          useValue: createMockRepository(),
        },
        {
          provide: 'USERS_REPOSITORY',
          useValue: createMockRepository(),
        },
      ],
    }).compile();

    module.useLogger(logger);

    service = module.get<PaymentService>(PaymentService);
    stripeCustomerService = module.get<StripeCustomerService>(
      StripeCustomerService
    );
    stripeSetupIntentService = module.get<StripeSetupIntentService>(
      StripeSetupIntentService
    );
    stripePaymentIntentService = module.get<StripePaymentIntentService>(
      StripePaymentIntentService
    );
    usersApi = module.get(UsersApi);
    billingAccountsRepository = module.get<MockRepository>(
      'BILLING_ACCOUNTS_REPOSITORY'
    );
    billingAccountsRepository.create &&
      billingAccountsRepository.create.mockImplementation(() =>
        Promise.resolve()
      );
    billingEventsRepository = module.get<MockRepository>(
      'BILLING_EVENTS_REPOSITORY'
    );
    billingEventsRepository.update &&
      billingEventsRepository.update.mockImplementation(() =>
        Promise.resolve()
      );
    usersRepository = module.get<MockRepository>('USERS_REPOSITORY');
  });
  beforeEach(() => {
    jest.clearAllMocks();
    billingAccountsRepository.findOne &&
      billingAccountsRepository.findOne.mockReturnValue(null);
    billingEventsRepository.findOne &&
      billingEventsRepository.findOne.mockReturnValue(null);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
    expect(stripeSetupIntentService).toBeDefined();
    expect(stripeCustomerService).toBeDefined();
    expect(stripePaymentIntentService).toBeDefined();
  });

  describe('setupIntent', () => {
    it('should setup intent"', async () => {
      jest.spyOn(usersApi, 'userControllerGetUser').mockResolvedValueOnce({
        status: 200,
        data: driverUser,
      } as AxiosResponse);
      const mockCreateCustomer = jest
        .spyOn(stripeCustomerService, 'create')
        .mockResolvedValueOnce({ customerId });
      const mockCreateIntent = jest
        .spyOn(stripeSetupIntentService, 'createIntent')
        .mockResolvedValueOnce(setupIntentResponse);
      billingAccountsRepository.findOne &&
        billingAccountsRepository.findOne.mockReturnValue({
          id: 123,
          userId: userIdAsNumber,
          currency: 'GBP',
          balance: 0.2,
        });
      const result = await service.setupIntent(uid);
      expect(logger.log).toHaveBeenCalledWith(
        { uid },
        'About to setup an intent',
        PaymentService.name
      );
      expect(usersApi.userControllerGetUser).toHaveBeenCalledWith(uid);
      expect(mockCreateCustomer).toHaveBeenCalledWith({
        email: driverUser.email,
        name: `${driverUser.first_name} ${driverUser.last_name}`,
      });
      expect(mockCreateIntent).toHaveBeenCalledWith(customerId);
      expect(billingAccountsRepository.update).toHaveBeenCalledWith(
        {
          paymentProcessorId: customerId,
        },
        { where: { id: 123 } }
      );
      expect(result).toEqual(setupIntentResponse);
    });

    it('should throw a CreateCustomerErrors when customer creation fails"', async () => {
      jest.spyOn(usersApi, 'userControllerGetUser').mockResolvedValueOnce({
        status: 200,
        data: driverUser,
      } as AxiosResponse);
      const error = new CreateCustomerErrors();
      const mockCreateCustomer = jest
        .spyOn(stripeCustomerService, 'create')
        .mockRejectedValue(error);
      await expect(service.setupIntent(uid)).rejects.toThrow(error);
      expect(mockCreateCustomer).toHaveBeenCalledWith({
        email: driverUser.email,
        name: `${driverUser.first_name} ${driverUser.last_name}`,
      });
      expect(billingAccountsRepository.create).not.toHaveBeenCalled();
      expect(billingAccountsRepository.update).not.toHaveBeenCalled();
      expect(logger.error).toHaveBeenCalledWith(
        { error, uid },
        'failed to create a customer',
        PaymentService.name
      );
    });

    it('should throw a SetupIntentError when creating intent fails"', async () => {
      const error = new SetupIntentErrors();
      jest.spyOn(usersApi, 'userControllerGetUser').mockResolvedValueOnce({
        status: 200,
        data: driverUser,
      } as AxiosResponse);
      const mockCreateCustomer = jest
        .spyOn(stripeCustomerService, 'create')
        .mockResolvedValueOnce({ customerId });
      const mockCreateIntent = jest
        .spyOn(stripeSetupIntentService, 'createIntent')
        .mockRejectedValue(error);
      usersRepository.findOne &&
        usersRepository.findOne.mockReturnValue({
          id: userIdAsNumber,
        });
      await expect(service.setupIntent(uid)).rejects.toThrow(error);
      expect(mockCreateCustomer).toHaveBeenCalledWith({
        email: driverUser.email,
        name: `${driverUser.first_name} ${driverUser.last_name}`,
      });
      expect(billingAccountsRepository.create).toHaveBeenCalledWith(
        expect.objectContaining({
          balance: 0,
          businessName: '',
          country: '',
          currency: 'GBP',
          line1: '',
          line2: '',
          postalTown: '',
          postcode: '',
          uid: expect.any(String),
          userId: 123,
        })
      );
      expect(mockCreateIntent).toHaveBeenCalledWith(customerId);
      expect(logger.error).toHaveBeenCalledWith(
        { error, uid },
        'failed to create a setup intent',
        PaymentService.name
      );
    });
  });

  describe('initializeGuestCharge', () => {
    it('should create payment intent', async () => {
      const mockCreatePaymentIntent = jest
        .spyOn(stripePaymentIntentService, 'createGuestPaymentIntent')
        .mockResolvedValue({
          paymentIntent: paymentIntentResponse.paymentIntent,
        });

      const res = await service.initializeGuestCharge(PAYMENT_REQUEST);

      expect(res).toStrictEqual({
        paymentIntent: paymentIntentResponse.paymentIntent,
      });

      expect(mockCreatePaymentIntent).toHaveBeenCalledTimes(1);
      expect(mockCreatePaymentIntent).toHaveBeenCalledWith(
        PAYMENT_REQUEST,
        {
          origin: 'billing-api',
          product: 'Guest Charge',
        },
        {
          card: {
            request_three_d_secure: 'challenge',
          },
        }
      );
    });

    it('should throw a PaymentIntentErrors when payment intent fails', async () => {
      const mockCreatePaymentIntent = jest
        .spyOn(stripePaymentIntentService, 'createGuestPaymentIntent')
        .mockImplementation(() => {
          throw new Error();
        });

      await expect(
        service.initializeGuestCharge(PAYMENT_REQUEST)
      ).rejects.toThrow(PaymentIntentErrors);

      expect(mockCreatePaymentIntent).toHaveBeenCalledTimes(1);
      expect(mockCreatePaymentIntent).toHaveBeenCalledWith(
        PAYMENT_REQUEST,
        {
          origin: 'billing-api',
          product: 'Guest Charge',
        },
        {
          card: {
            request_three_d_secure: 'challenge',
          },
        }
      );
    });
  });

  describe('initializePaymentSheet', () => {
    it('should create payment intent for a new customer', async () => {
      const mockCreateCustomer = jest
        .spyOn(stripeCustomerService, 'create')
        .mockResolvedValueOnce({ customerId });
      const mockCreatePaymentIntent = jest
        .spyOn(stripePaymentIntentService, 'createRegisteredUserPaymentIntent')
        .mockResolvedValueOnce(paymentIntentResponse);
      jest.spyOn(usersApi, 'userControllerGetUser').mockResolvedValueOnce({
        status: 200,
        data: driverUser,
      } as AxiosResponse);
      const result = await service.initializePaymentSheet(PAYMENT_REQUEST, uid);
      expect(logger.log).toHaveBeenCalledWith(
        { request: PAYMENT_REQUEST, uid },
        'About to create a payment intent',
        PaymentService.name
      );
      expect(usersApi.userControllerGetUser).toHaveBeenCalledWith(user.uuid);
      expect(mockCreateCustomer).toHaveBeenCalledWith({
        email: driverUser.email,
        name: `${driverUser.first_name} ${driverUser.last_name}`,
      });
      expect(billingAccountsRepository.create).toHaveBeenCalledWith(
        expect.objectContaining({
          balance: 0,
          businessName: '',
          country: '',
          currency: 'GBP',
          line1: '',
          line2: '',
          postalTown: '',
          postcode: '',
          uid: expect.any(String),
          userId: 123,
        })
      );
      expect(mockCreatePaymentIntent).toHaveBeenCalledWith(
        PAYMENT_REQUEST,
        customerId,
        metadata,
        paymentMethodOptions
      );
      expect(result).toEqual(paymentIntentResponse);
    });

    it('should create payment intent for an existing customer', async () => {
      const mockCreateCustomer = jest
        .spyOn(stripeCustomerService, 'create')
        .mockResolvedValueOnce({ customerId });
      const mockCreatePaymentIntent = jest
        .spyOn(stripePaymentIntentService, 'createRegisteredUserPaymentIntent')
        .mockResolvedValueOnce(paymentIntentResponse);
      jest.spyOn(usersApi, 'userControllerGetUser').mockResolvedValueOnce({
        status: 200,
        data: driverUserWithStripeCard,
      } as AxiosResponse);
      const result = await service.initializePaymentSheet(PAYMENT_REQUEST, uid);
      expect(logger.log).toHaveBeenCalledWith(
        { request: PAYMENT_REQUEST, uid },
        'About to create a payment intent',
        PaymentService.name
      );
      expect(usersApi.userControllerGetUser).toHaveBeenCalledWith(user.uuid);
      expect(mockCreateCustomer).not.toHaveBeenCalled();
      expect(billingAccountsRepository.create).not.toHaveBeenCalled();
      expect(billingAccountsRepository.update).not.toHaveBeenCalled();
      expect(mockCreatePaymentIntent).toHaveBeenCalledWith(
        PAYMENT_REQUEST,
        customerId,
        metadata,
        paymentMethodOptions
      );
      expect(result).toEqual(paymentIntentResponse);
    });

    it('should throw a PaymentFailedError when payment intent fails', async () => {
      const error = new PaymentIntentErrors();
      jest.spyOn(usersApi, 'userControllerGetUser').mockResolvedValueOnce({
        status: 200,
        data: driverUserWithStripeCard,
      } as AxiosResponse);
      const mockCreatePayment = jest
        .spyOn(stripePaymentIntentService, 'createRegisteredUserPaymentIntent')
        .mockRejectedValue(error);
      await expect(
        service.initializePaymentSheet(PAYMENT_REQUEST, uid)
      ).rejects.toThrow(error);
      expect(usersApi.userControllerGetUser).toHaveBeenCalledWith(uid);
      expect(billingAccountsRepository.create).not.toHaveBeenCalled();
      expect(billingAccountsRepository.update).not.toHaveBeenCalled();
      expect(mockCreatePayment).toHaveBeenCalledWith(
        PAYMENT_REQUEST,
        driverUserWithStripeCard.paymentProcessorId,
        metadata,
        paymentMethodOptions
      );
      expect(logger.error).toHaveBeenCalledWith(
        { error, request: PAYMENT_REQUEST },
        'failed to create a payment intent',
        PaymentService.name
      );
    });

    it('should throw an error when getting a user fails', async () => {
      const error = new Error('There was an error');
      jest
        .spyOn(usersApi, 'userControllerGetUser')
        .mockRejectedValueOnce(error);
      await expect(
        service.initializePaymentSheet(PAYMENT_REQUEST, uid)
      ).rejects.toThrow(error);
      expect(usersApi.userControllerGetUser).toHaveBeenCalledWith(uid);
      expect(billingAccountsRepository.create).not.toHaveBeenCalled();
      expect(billingAccountsRepository.update).not.toHaveBeenCalled();
      expect(logger.error).toHaveBeenCalledWith(
        { uid },
        error,
        PaymentService.name
      );
    });
  });

  describe('capturePaymentIntent', () => {
    it('should successfully capture payment intent with the requested amount', async () => {
      const request: CapturePaymentRequest = {
        billingEventId: 12345,
        amountToCapture: 5000,
      };

      billingEventsRepository.findOne &&
        billingEventsRepository.findOne.mockReturnValue({
          transactionId: paymentIntentId,
        });

      jest
        .spyOn(stripePaymentIntentService, 'retrievePaymentIntent')
        .mockResolvedValueOnce({
          paymentIntentStatus: 'requires_capture',
          amountCapturable: 5000,
        });
      const mockCapturePaymentIntent = jest
        .spyOn(stripePaymentIntentService, 'capturePaymentIntent')
        .mockResolvedValueOnce({
          paymentIntentStatus: 'succeeded',
          amountReceived: 3000,
          currency: 'GBP',
        });

      const res = await service.capturePaymentIntent(request);

      expect(billingEventsRepository.findOne).toHaveBeenCalledWith({
        attributes: ['transactionId', 'processedAt'],
        raw: true,
        where: {
          id: request.billingEventId,
        },
      });

      expect(
        stripePaymentIntentService.retrievePaymentIntent
      ).toHaveBeenCalledWith(paymentIntentId);
      expect(mockCapturePaymentIntent).toHaveBeenCalledWith({
        paymentIntentId,
        amount: 5000,
        metadata: { origin: 'billing-api' },
      });
      expect(billingEventsRepository.update).toHaveBeenCalledWith(
        expect.objectContaining({
          presentmentAmount: 3000,
          presentmentCurrency: 'GBP',
          settlementAmount: 3000,
          exchangeRate: '1',
          processedAt: expect.any(Date),
        }),
        expect.objectContaining({
          where: {
            id: request.billingEventId,
          },
        })
      );

      expect(res).toEqual({
        paymentIntentStatus: 'succeeded',
        amountReceived: 3000,
        currency: 'GBP',
      });
    });
    it('should throw an error when the payment intent is cancelled', async () => {
      const request: CapturePaymentRequest = {
        billingEventId: 12345,
        amountToCapture: 5000,
      };

      billingEventsRepository.findOne &&
        billingEventsRepository.findOne.mockReturnValue({
          transactionId: paymentIntentId,
        });

      jest
        .spyOn(stripePaymentIntentService, 'retrievePaymentIntent')
        .mockResolvedValueOnce({
          paymentIntentStatus: 'canceled',
          amountCapturable: 0,
        });

      await expect(service.capturePaymentIntent(request)).rejects.toThrow(
        PaymentIntentErrors
      );

      expect(billingEventsRepository.findOne).toHaveBeenCalledWith({
        attributes: ['transactionId', 'processedAt'],
        raw: true,
        where: {
          id: request.billingEventId,
        },
      });

      expect(billingEventsRepository.update).not.toHaveBeenCalled();
    });
    it('should throw an error and release the payment when capturing payment intent fails', async () => {
      const request: CapturePaymentRequest = {
        billingEventId: 12345,
        amountToCapture: 5000,
      };

      billingEventsRepository.findOne &&
        billingEventsRepository.findOne.mockReturnValue({
          transactionId: paymentIntentId,
        });

      jest
        .spyOn(stripePaymentIntentService, 'retrievePaymentIntent')
        .mockResolvedValueOnce({
          paymentIntentStatus: 'requires_capture',
          amountCapturable: 5000,
        });

      const mockCapturePaymentIntent = jest
        .spyOn(stripePaymentIntentService, 'capturePaymentIntent')
        .mockRejectedValueOnce(new Error('Capture failed'));

      await expect(service.capturePaymentIntent(request)).rejects.toThrow(
        PaymentIntentErrors
      );

      expect(billingEventsRepository.findOne).toHaveBeenCalledWith({
        attributes: ['transactionId', 'processedAt'],
        raw: true,
        where: {
          id: request.billingEventId,
        },
      });

      expect(
        stripePaymentIntentService.retrievePaymentIntent
      ).toHaveBeenCalledWith(paymentIntentId);

      expect(mockCapturePaymentIntent).toHaveBeenCalledWith({
        paymentIntentId,
        amount: 5000,
        metadata: { origin: 'billing-api' },
      });

      expect(logger.error).toHaveBeenCalledWith(
        { error: new Error('Capture failed'), paymentIntentId },
        'failed to capture payment intent',
        PaymentService.name
      );

      expect(
        stripePaymentIntentService.cancelPaymentIntent
      ).toHaveBeenCalledWith({
        paymentIntentId,
        cancellationReason: 'abandoned',
      });
      expect(billingEventsRepository.update).toHaveBeenCalledWith(
        expect.objectContaining({
          processedAt: expect.any(Date),
        }),
        expect.objectContaining({
          where: {
            id: request.billingEventId,
          },
        })
      );
    });
    it('should capture the capturable amount when amount to capture is too large', async () => {
      const request: CapturePaymentRequest = {
        billingEventId: 12345,
        amountToCapture: 5000,
      };

      billingEventsRepository.findOne &&
        billingEventsRepository.findOne.mockReturnValue({
          transactionId: paymentIntentId,
        });

      jest
        .spyOn(stripePaymentIntentService, 'retrievePaymentIntent')
        .mockResolvedValueOnce({
          paymentIntentStatus: 'requires_capture',
          amountCapturable: 3000,
        });

      const mockCapturePaymentIntent = jest
        .spyOn(stripePaymentIntentService, 'capturePaymentIntent')
        .mockRejectedValueOnce(new StripeAmountTooLargeException())
        .mockResolvedValueOnce({
          paymentIntentStatus: 'succeeded',
          amountReceived: 3000,
          currency: 'GBP',
        });

      const res = await service.capturePaymentIntent(request);

      expect(billingEventsRepository.findOne).toHaveBeenCalledWith({
        attributes: ['transactionId', 'processedAt'],
        raw: true,
        where: {
          id: request.billingEventId,
        },
      });

      expect(mockCapturePaymentIntent).toHaveBeenCalledWith({
        paymentIntentId,
        amount: 5000,
        metadata: { origin: 'billing-api' },
      });

      expect(
        stripePaymentIntentService.retrievePaymentIntent
      ).toHaveBeenCalledWith(paymentIntentId);

      expect(mockCapturePaymentIntent).toHaveBeenNthCalledWith(1, {
        paymentIntentId,
        amount: 5000,
        metadata: { origin: 'billing-api' },
      });
      expect(mockCapturePaymentIntent).toHaveBeenNthCalledWith(2, {
        paymentIntentId,
        amount: 3000,
        metadata: { origin: 'billing-api' },
      });
      expect(billingEventsRepository.update).toHaveBeenCalledWith(
        expect.objectContaining({
          presentmentAmount: 3000,
          presentmentCurrency: 'GBP',
          settlementAmount: 3000,
          exchangeRate: '1',
          processedAt: expect.any(Date),
        }),
        expect.objectContaining({
          where: {
            id: request.billingEventId,
          },
        })
      );
      expect(res).toEqual({
        paymentIntentStatus: 'succeeded',
        amountReceived: 3000,
        currency: 'GBP',
      });
    });
    it('should throw an error when cancelling the payment intent fails', async () => {
      billingEventsRepository.findOne &&
        billingEventsRepository.findOne.mockReturnValue({
          transactionId: paymentIntentId,
        });

      jest
        .spyOn(stripePaymentIntentService, 'retrievePaymentIntent')
        .mockResolvedValueOnce({
          paymentIntentStatus: 'requires_capture',
          amountCapturable: 5000,
        });

      const mockCapturePaymentIntent = jest
        .spyOn(stripePaymentIntentService, 'capturePaymentIntent')
        .mockRejectedValueOnce(new Error('Capture failed'));

      const mockCancelPaymentIntent = jest
        .spyOn(stripePaymentIntentService, 'cancelPaymentIntent')
        .mockRejectedValue(new Error('Cancel failed'));

      const request: CapturePaymentRequest = {
        billingEventId: 12345,
        amountToCapture: 1000,
      };

      await expect(service.capturePaymentIntent(request)).rejects.toThrow(
        new PaymentIntentErrors()
      );

      expect(billingEventsRepository.findOne).toHaveBeenCalledWith({
        attributes: ['transactionId', 'processedAt'],
        raw: true,
        where: {
          id: request.billingEventId,
        },
      });

      expect(mockCapturePaymentIntent).toHaveBeenCalledWith({
        paymentIntentId: paymentIntentId,
        amount: request.amountToCapture,
        metadata: { origin: 'billing-api' },
      });
      expect(mockCancelPaymentIntent).toHaveBeenCalledWith({
        paymentIntentId: paymentIntentId,
        cancellationReason: 'abandoned',
      });
    });
    it('should throw an error when billing event has already been processed', async () => {
      const request: CapturePaymentRequest = {
        billingEventId: 12345,
        amountToCapture: 5000,
      };

      billingEventsRepository.findOne &&
        billingEventsRepository.findOne.mockReturnValue({
          transactionId: paymentIntentId,
          processedAt: new Date(2025, 1, 1),
        });

      await expect(service.capturePaymentIntent(request)).rejects.toThrow(
        PaymentIntentErrors
      );

      expect(billingEventsRepository.findOne).toHaveBeenCalledWith({
        attributes: ['transactionId', 'processedAt'],
        raw: true,
        where: {
          id: request.billingEventId,
        },
      });
    });
  });
});
