import { AxiosResponse } from 'axios';
import {
  BadRequestException,
  INestApplication,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import {
  BillingAccounts,
  PodadminSequelizeModule,
  Users,
} from '@experience/shared/sequelize/podadmin';
import { BillingEventService } from './billing.event.service';
import { ConfigService } from '@nestjs/config';
import {
  ExtendedUserInfoResponseDto,
  UsersApi,
} from '@experience/driver-account-api/api-client';
import { NotificationService } from '../notification/notification.service';
import { PAYG_TOP_UP_CARD, PAYG_TOP_UP_TOKEN } from './billing.event.constants';
import { PaymentNotProcessedError } from './billing.event.error';
import { QueueConsumerService } from '@experience/shared/nest/aws/sqs-module';
import { StripeChargeService } from '@experience/shared/nest/stripe';
import { Test } from '@nestjs/testing';
import { Transaction } from 'sequelize';
import { createMock } from '@golevelup/ts-jest';
import { jest } from '@jest/globals';
import { mockDeep } from 'jest-mock-extended';
import { plainToInstance } from 'class-transformer';
import mockEvent, {
  MockRepository,
  createMockRepository,
  mockFailedEvent,
  mockFailedPaymentIntent,
  mockPaymentIntent,
} from '../test.utils';

jest.mock('@experience/shared/nest/stripe');

describe('BillingEventService', () => {
  let service: BillingEventService;
  let notificationService: NotificationService;
  let usersApi: UsersApi;
  const logger = mockDeep<Logger>();
  let billingAccountsRepository: MockRepository;
  let billingEventsRepository: MockRepository;
  let usersRepository: MockRepository;
  const userId = '123';
  const userIdAsNumber = parseInt(userId);
  let queueConsumerService: QueueConsumerService;
  let stripeChargeService: StripeChargeService;
  let app: INestApplication;
  const transaction = mockDeep<Transaction>();

  beforeEach(async () => {
    jest.clearAllMocks();
    const module = await Test.createTestingModule({
      imports: [PodadminSequelizeModule],
      providers: [
        BillingEventService,
        {
          provide: QueueConsumerService,
          useValue: { start: jest.fn(), setMessageHandler: jest.fn() },
        },
        ConfigService,
        {
          provide: 'BILLING_ACCOUNTS_REPOSITORY',
          useValue: createMockRepository(),
        },
        {
          provide: 'BILLING_EVENTS_REPOSITORY',
          useValue: createMockRepository(),
        },
        {
          provide: 'USERS_REPOSITORY',
          useValue: createMockRepository(),
        },
        {
          provide: UsersApi,
          useValue: new UsersApi(),
        },
        {
          provide: NotificationService,
          useValue: createMock<NotificationService>(),
        },
        StripeChargeService,
      ],
    }).compile();

    app = module.createNestApplication();
    //for app bootstrap test;
    await app.init();

    module.useLogger(logger);

    service = module.get<BillingEventService>(BillingEventService);
    notificationService = module.get<NotificationService>(NotificationService);
    usersApi = module.get<UsersApi>(UsersApi);
    billingAccountsRepository = module.get<MockRepository>(
      'BILLING_ACCOUNTS_REPOSITORY'
    );
    billingEventsRepository = module.get<MockRepository>(
      'BILLING_EVENTS_REPOSITORY'
    );
    queueConsumerService = module.get(QueueConsumerService);
    if (billingAccountsRepository.create) {
      billingAccountsRepository.create.mockImplementation(() =>
        Promise.resolve()
      );
    }
    usersRepository = module.get<MockRepository>('USERS_REPOSITORY');
    if (usersRepository.findOne) {
      usersRepository.findOne.mockReturnValue({
        id: userIdAsNumber,
      });
    }
    jest.spyOn(service, 'getTransaction').mockResolvedValue(transaction);

    stripeChargeService = module.get<StripeChargeService>(StripeChargeService);
  });

  afterEach(async () => {
    await app.close();
  });

  describe('listen to billing event', () => {
    it('should create a billing event', async () => {
      const { amount, currency } = mockPaymentIntent;

      jest.spyOn(usersApi, 'userControllerGetUser').mockResolvedValue({
        status: 200,
        data: {
          uid: 'a10cb934-f181-47a5-b181-272f61a30ff9',
        },
      } as AxiosResponse);

      const billingAccounts = plainToInstance(BillingAccounts, {
        id: 123,
        userId: userIdAsNumber,
        currency: 'GBP',
        balance: 0.2,
        paymentProcessorId: 'cus_QOUbwhpAZFRPvb',
        user: { authId: 'a10cb934-f181-47a5-b181-272f61a30ff9' },
      });

      if (billingAccountsRepository.findOne) {
        billingAccountsRepository.findOne.mockReturnValue(billingAccounts);
      }

      if (billingEventsRepository.count) {
        billingEventsRepository.count.mockReturnValue(0);
      }

      if (billingEventsRepository.create) {
        billingEventsRepository.create.mockImplementation(() =>
          Promise.resolve()
        );
      }

      jest
        .spyOn(notificationService, 'sendTopUpConfirmationEmail')
        .mockResolvedValue();

      await service.handleMessageAndSendEmail({
        Body: JSON.stringify(mockPaymentIntent),
      });

      expect(billingAccountsRepository.findOne).toHaveBeenCalledWith({
        include: [
          {
            model: Users,
            as: 'user',
            paranoid: false,
          },
        ],
        where: {
          paymentProcessorId: 'cus_QOUbwhpAZFRPvb',
        },
        paranoid: false,
      });

      expect(billingEventsRepository.count).toHaveBeenCalledWith({
        where: {
          transactionId: mockPaymentIntent.id,
        },
      });

      expect(logger.log).toHaveBeenCalledWith(
        `billing account with customer id ${billingAccounts.paymentProcessorId}, userId ${billingAccounts.user.authId} and balance ${billingAccounts.balance} is found`,
        BillingEventService.name
      );

      const processedAt = new Date(mockPaymentIntent.created * 1000);

      expect(billingEventsRepository.create).toHaveBeenCalledWith(
        expect.objectContaining({
          accountId: billingAccounts.id,
          presentmentAmount: amount,
          presentmentCurrency: currency.toUpperCase(),
          exchangeRate: '1',
          settlementAmount: amount,
          settlementCurrency: currency.toUpperCase(),
          transactionProvider: 'stripe',
          transactionId: mockPaymentIntent.id,
          description: PAYG_TOP_UP_CARD,
          refundedAmount: 0,
          userId: billingAccounts.userId,
          createdAt: expect.any(Date),
          processedAt: processedAt,
          updatedAt: expect.any(Date),
        }),
        { transaction: transaction }
      );

      expect(logger.log).toHaveBeenCalledWith(
        `billing event for userId ${billingAccounts.user.authId} has been created for ${mockPaymentIntent.id}`,
        BillingEventService.name
      );

      const newBalance = billingAccounts.balance + 2000;

      expect(billingAccountsRepository.update).toHaveBeenCalledWith(
        {
          balance: newBalance,
        },
        {
          where: {
            id: billingAccounts.id,
          },
          transaction,
        }
      );

      expect(logger.log).toHaveBeenCalledWith(
        `The balance for userId ${billingAccounts.user.authId} is ${newBalance} in pence after ${mockPaymentIntent.id}`,
        BillingEventService.name
      );
    });

    it('should not process a billing event if it does not have a customer id', async () => {
      await expect(
        service.handleMessageAndSendEmail({
          Body: JSON.stringify({ ...mockPaymentIntent, customer: null }),
        })
      ).rejects.toThrow(BadRequestException);
    });

    it('should not create a billing event if it already exists', async () => {
      jest.spyOn(usersApi, 'userControllerGetUser').mockResolvedValue({
        status: 200,
        data: {
          uid: 'a10cb934-f181-47a5-b181-272f61a30ff9',
        },
      } as AxiosResponse);

      const billingAccounts = plainToInstance(BillingAccounts, {
        id: 123,
        userId: userIdAsNumber,
        currency: 'GBP',
        balance: 0.2,
        paymentProcessorId: 'cus_QOUbwhpAZFRPvb',
        user: { authId: 'a10cb934-f181-47a5-b181-272f61a30ff9' },
      });

      if (billingAccountsRepository.findOne) {
        billingAccountsRepository.findOne.mockReturnValue(billingAccounts);
      }

      if (billingEventsRepository.count) {
        billingEventsRepository.count.mockReturnValue(1);
      }

      if (billingEventsRepository.create) {
        billingEventsRepository.create.mockImplementation(() =>
          Promise.resolve()
        );
      }

      await service.handleMessageAndSendEmail({
        Body: JSON.stringify(mockPaymentIntent),
      });

      expect(billingEventsRepository.create).not.toHaveBeenCalled();
      expect(billingAccountsRepository.update).not.toHaveBeenCalled();
    });

    it('should throw an exception if the billing account has a deletedAt date', async () => {
      jest.spyOn(usersApi, 'userControllerGetUser').mockResolvedValue({
        status: 200,
        data: {
          uid: 'a10cb934-f181-47a5-b181-272f61a30ff9',
        },
      } as AxiosResponse);

      const billingAccounts = plainToInstance(BillingAccounts, {
        id: 123,
        userId: userIdAsNumber,
        currency: 'GBP',
        balance: 0.2,
        paymentProcessorId: 'cus_QOUbwhpAZFRPvb',
        user: { authId: 'a10cb934-f181-47a5-b181-272f61a30ff9' },
        deletedAt: new Date(Date.now()),
      });

      if (billingAccountsRepository.findOne) {
        billingAccountsRepository.findOne.mockReturnValue(billingAccounts);
      }

      if (billingEventsRepository.count) {
        billingEventsRepository.count.mockReturnValue(0);
      }

      await expect(
        service.handleMessageAndSendEmail({
          Body: JSON.stringify(mockPaymentIntent),
        })
      ).rejects.toThrow(PaymentNotProcessedError);

      expect(billingEventsRepository.create).not.toHaveBeenCalled();
      expect(billingAccountsRepository.update).not.toHaveBeenCalled();
    });

    it('should throw an exception if the billing account user has a deletedAt date', async () => {
      jest.spyOn(usersApi, 'userControllerGetUser').mockResolvedValue({
        status: 200,
        data: {
          uid: 'a10cb934-f181-47a5-b181-272f61a30ff9',
        },
      } as AxiosResponse);

      const billingAccounts = plainToInstance(BillingAccounts, {
        id: 123,
        userId: userIdAsNumber,
        currency: 'GBP',
        balance: 0.2,
        paymentProcessorId: 'cus_QOUbwhpAZFRPvb',
        user: {
          authId: 'a10cb934-f181-47a5-b181-272f61a30ff9',
          deletedAt: new Date(Date.now()),
        },
      });

      if (billingAccountsRepository.findOne) {
        billingAccountsRepository.findOne.mockReturnValue(billingAccounts);
      }

      if (billingEventsRepository.count) {
        billingEventsRepository.count.mockReturnValue(0);
      }

      await expect(
        service.handleMessageAndSendEmail({
          Body: JSON.stringify(mockPaymentIntent),
        })
      ).rejects.toThrow(PaymentNotProcessedError);

      expect(billingEventsRepository.create).not.toHaveBeenCalled();
      expect(billingAccountsRepository.update).not.toHaveBeenCalled();
    });

    it.each([
      [
        'Apple Pay',
        { cardPaymentType: 'apple_pay', expected: PAYG_TOP_UP_TOKEN },
      ],
      [
        'Google Pay',
        { cardPaymentType: 'google_pay', expected: PAYG_TOP_UP_TOKEN },
      ],
    ])(
      'should create a billing event with an appropriate description for %s',
      async (_, data) => {
        const paymentIntent = {
          ...mockPaymentIntent,
          latest_charge: 'chargeId',
        };
        const { amount, currency } = paymentIntent;

        jest.spyOn(usersApi, 'userControllerGetUser').mockResolvedValue({
          status: 200,
          data: {
            uid: 'a10cb934-f181-47a5-b181-272f61a30ff9',
          },
        } as AxiosResponse);

        jest
          .spyOn(stripeChargeService, 'isPaymentFromWallet')
          .mockResolvedValueOnce(true);

        const billingAccounts = plainToInstance(BillingAccounts, {
          id: 123,
          userId: userIdAsNumber,
          currency: 'GBP',
          balance: 0.2,
          paymentProcessorId: 'cus_QOUbwhpAZFRPvb',
          user: { authId: 'a10cb934-f181-47a5-b181-272f61a30ff9' },
        });

        if (billingAccountsRepository.findOne) {
          billingAccountsRepository.findOne.mockReturnValue(billingAccounts);
        }

        if (billingEventsRepository.create) {
          billingEventsRepository.create.mockImplementation(() =>
            Promise.resolve()
          );
        }

        jest
          .spyOn(notificationService, 'sendTopUpConfirmationEmail')
          .mockResolvedValue();

        await service.handleMessageAndSendEmail({
          Body: JSON.stringify(paymentIntent),
        });

        expect(billingAccountsRepository.findOne).toHaveBeenCalledWith({
          include: [
            {
              model: Users,
              as: 'user',
              paranoid: false,
            },
          ],
          where: {
            paymentProcessorId: 'cus_QOUbwhpAZFRPvb',
          },
          paranoid: false,
        });

        expect(logger.log).toHaveBeenCalledWith(
          `billing account with customer id ${billingAccounts.paymentProcessorId}, userId ${billingAccounts.user.authId} and balance ${billingAccounts.balance} is found`,
          BillingEventService.name
        );

        const processedAt = new Date(paymentIntent.created * 1000);

        expect(billingEventsRepository.create).toHaveBeenCalledWith(
          expect.objectContaining({
            accountId: billingAccounts.id,
            presentmentAmount: amount,
            presentmentCurrency: currency.toUpperCase(),
            exchangeRate: '1',
            settlementAmount: amount,
            settlementCurrency: currency.toUpperCase(),
            transactionProvider: 'stripe',
            transactionId: paymentIntent.id,
            description: data.expected,
            refundedAmount: 0,
            userId: billingAccounts.userId,
            createdAt: expect.any(Date),
            processedAt: processedAt,
            updatedAt: expect.any(Date),
          }),
          { transaction: transaction }
        );

        expect(logger.log).toHaveBeenCalledWith(
          `billing event for userId ${billingAccounts.user.authId} has been created for ${paymentIntent.id}`,
          BillingEventService.name
        );

        const newBalance = billingAccounts.balance + 2000;

        expect(billingAccountsRepository.update).toHaveBeenCalledWith(
          {
            balance: newBalance,
          },
          {
            where: {
              id: billingAccounts.id,
            },
            transaction,
          }
        );

        expect(logger.log).toHaveBeenCalledWith(
          `The balance for userId ${billingAccounts.user.authId} is ${newBalance} in pence after ${paymentIntent.id}`,
          BillingEventService.name
        );
      }
    );

    it('sends an email notification for the billing event', async () => {
      const sendEmailMock = jest.spyOn(
        notificationService,
        'sendTopUpConfirmationEmail'
      );

      const mockUser: Partial<ExtendedUserInfoResponseDto> = {
        uid: 'e18ff630-e055-4bf2-981a-f0523d41baeb',
      };

      const mockBillingAccount = plainToInstance(BillingAccounts, {
        user: {
          authId: mockUser.uid,
        },
      });

      jest.spyOn(usersApi, 'userControllerGetUser').mockResolvedValue({
        status: 200,
        data: mockUser,
      } as AxiosResponse);

      jest
        .spyOn(billingAccountsRepository, 'findOne')
        .mockReturnValue(mockBillingAccount);

      await service.handleMessageAndSendEmail({
        Body: JSON.stringify(mockEvent.data.object),
      });

      expect(usersRepository.findOne).toHaveBeenCalledWith({
        raw: true,
        where: {
          id: mockBillingAccount.userId,
        },
      });
      expect(sendEmailMock).toHaveBeenCalledTimes(1);
      expect(sendEmailMock).toHaveBeenCalledWith(
        mockPaymentIntent,
        mockUser,
        mockBillingAccount
      );
    });

    it('does not send an email notification for the billing event when user not found by userApi', async () => {
      const sendEmailMock = jest.spyOn(
        notificationService,
        'sendTopUpConfirmationEmail'
      );

      const mockBillingAccount = plainToInstance(BillingAccounts, {
        user: {
          authId: '65b89e2d-ebc9-4235-9e05-5fa75c7fa477',
        },
      });

      const error = new Error('user not found');

      jest
        .spyOn(usersApi, 'userControllerGetUser')
        .mockRejectedValueOnce(error);

      jest
        .spyOn(billingAccountsRepository, 'findOne')
        .mockReturnValue(mockBillingAccount);

      await expect(
        service.handleMessageAndSendEmail({
          Body: JSON.stringify(mockEvent.data.object),
        })
      ).rejects.toThrow(error);

      expect(sendEmailMock).toHaveBeenCalledTimes(0);
    });

    it('does not send an email notification for the billing event when user not found by podadmin', async () => {
      const sendEmailMock = jest.spyOn(
        notificationService,
        'sendTopUpConfirmationEmail'
      );
      const mockBillingAccount = {
        user: { authId: 'a10cb934-f181-47a5-b181-272f61a30ff9' },
      };

      if (usersRepository.findOne) {
        usersRepository.findOne.mockReturnValue(null);
      }

      jest
        .spyOn(billingAccountsRepository, 'findOne')
        .mockReturnValue(mockBillingAccount);

      await service.handleMessageAndSendEmail({
        Body: JSON.stringify(mockEvent.data.object),
      });

      expect(sendEmailMock).toHaveBeenCalledTimes(0);
      expect(logger.warn).toHaveBeenCalledWith(
        `User with userId ${mockBillingAccount.user.authId} has not been found`,
        BillingEventService.name
      );
    });

    it('logs a failed payment intent billing event', async () => {
      const mockBillingAccount = plainToInstance(BillingAccounts, {
        user: { authId: 'a10cb934-f181-47a5-b181-272f61a30ff9' },
      });

      jest
        .spyOn(billingAccountsRepository, 'findOne')
        .mockReturnValue(mockBillingAccount);

      await service.handleMessageAndSendEmail({
        Body: JSON.stringify(mockFailedEvent.data.object),
      });

      expect(billingAccountsRepository.findOne).toHaveBeenCalledWith({
        include: [
          {
            model: Users,
            as: 'user',
          },
        ],
        where: {
          paymentProcessorId: mockFailedPaymentIntent.customer,
        },
      });

      expect(logger.error).toHaveBeenCalledWith(
        {
          customerId: mockFailedPaymentIntent.customer,
          userId: mockBillingAccount.user.authId,
          amount: mockFailedPaymentIntent.amount,
          paymentId: mockFailedPaymentIntent.id,
          error: mockFailedPaymentIntent.last_payment_error?.message,
        },
        'Stripe payment intent returned payment failed',
        BillingEventService.name
      );
    });

    it('logs a failed payment intent billing event with an unknown user when user is null', async () => {
      const mockBillingAccount = plainToInstance(BillingAccounts, {});

      jest
        .spyOn(billingAccountsRepository, 'findOne')
        .mockReturnValue(mockBillingAccount);

      await service.handleMessageAndSendEmail({
        Body: JSON.stringify(mockFailedEvent.data.object),
      });

      expect(billingAccountsRepository.findOne).toHaveBeenCalledWith({
        include: [
          {
            model: Users,
            as: 'user',
          },
        ],
        where: {
          paymentProcessorId: mockFailedPaymentIntent.customer,
        },
      });

      expect(logger.error).toHaveBeenCalledWith(
        {
          customerId: mockFailedPaymentIntent.customer,
          userId: 'unknown or disabled',
          amount: mockFailedPaymentIntent.amount,
          paymentId: mockFailedPaymentIntent.id,
          error: mockFailedPaymentIntent.last_payment_error?.message,
        },
        'Stripe payment intent returned payment failed',
        BillingEventService.name
      );
    });

    it('throws an error for failed payment intent billing event where the billing account can not be found', async () => {
      jest.spyOn(billingAccountsRepository, 'findOne').mockReturnValue(null);

      const error = new NotFoundException(
        `Stripe customer ID ${mockFailedPaymentIntent.customer} not found`
      );

      await expect(
        service.handleMessageAndSendEmail({
          Body: JSON.stringify(mockFailedEvent.data.object),
        })
      ).rejects.toThrow(error);
    });

    it('should start listening to queue on bootstrap', () => {
      expect(queueConsumerService.setMessageHandler).toHaveBeenCalledTimes(1);
      expect(queueConsumerService.start).toHaveBeenCalledTimes(1);
    });
  });
});
