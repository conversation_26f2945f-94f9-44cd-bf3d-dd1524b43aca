 
export default {
  displayName: 'billing-api',
  preset: '../../../jest.preset.js',
  testEnvironment: 'node',
  transform: {
    '^.+\\.[tj]s$': ['ts-jest', { tsconfig: '<rootDir>/tsconfig.spec.json' }],
  },
  moduleFileExtensions: ['ts', 'js', 'html'],
  coverageDirectory: '../../../coverage/apps/mobile/billing-api',
  setupFiles: ['<rootDir>/jest.env.ts'],
  testPathIgnorePatterns: ['index.spec.ts', '.module.spec.ts'],
};
