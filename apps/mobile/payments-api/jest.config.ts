 
export default {
  displayName: 'payments-api',
  preset: '../../../jest.preset.js',
  testEnvironment: 'node',
  transform: {
    '^.+\\.[tj]s$': ['ts-jest', { tsconfig: '<rootDir>/tsconfig.spec.json' }],
  },
  moduleFileExtensions: ['ts', 'js', 'html'],
  coverageDirectory: '../../../coverage/apps/mobile/payments-api',
  setupFiles: ['<rootDir>/jest.env.ts'],
  testPathIgnorePatterns: ['index.spec.ts', '.module.spec.ts'],
};
