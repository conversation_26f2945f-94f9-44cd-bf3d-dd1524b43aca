import { INestApplication, VersioningType } from '@nestjs/common';
import { bootstrap } from '@experience/shared/nest/utils';

 
import * as path from 'path';
import {
  DockerComposeEnvironment,
  StartedDockerComposeEnvironment,
  Wait,
} from 'testcontainers';
import { SetupServer, setupServer } from 'msw/node';
// eslint-disable-next-line @nx/enforce-module-boundaries
import {
  API_VERSION,
  describeAccountModule,
  describeHealthModule,
  describeInstallImagesModule,
  describeInstallsModule,
} from '../../../installer-api/src';
// eslint-disable-next-line @nx/enforce-module-boundaries
import { AppModule } from '../../../installer-api/src/app/app.module';
// eslint-disable-next-line @nx/enforce-module-boundaries
import { PCB_SWAPS_REPOSITORY } from '../../../installer-api/src/database/constants';
// eslint-disable-next-line @nx/enforce-module-boundaries
import { PcbSwap } from '../../../installer-api/src/database/entities/pcb-swap.entity';
import { Repository } from 'typeorm';
import { describeAuthModule } from '@experience/mobile/nest/auth/specs';
// eslint-disable-next-line @nx/enforce-module-boundaries
import { describePcbSwapsModule } from '../../../installer-api/src/pcb-swaps/pcb-swaps.module.spec';
import { getAuth } from '@experience/shared/firebase/admin';
import { useContainer } from 'class-validator';

const PORT = 5103;
const BASE_URL_NEUTRAL = `http://localhost:${PORT}`;
const BASE_URL_VERSIONED = `http://localhost:${PORT}/v${API_VERSION}`;

const seedDatabase = async (nestApi: INestApplication) => {
  const pcbSwapRepository =
    nestApi.get<Repository<PcbSwap>>(PCB_SWAPS_REPOSITORY);

  const nullPcbSwap = pcbSwapRepository.create({
    ppid: 'PSL-620893',
    serialNumber: '2422110742',
    status: 'success',
    changedAt: '2024-09-23T09:09:23.523Z',
    emailedAt: null,
  });

  const completePcbSwap = pcbSwapRepository.create({
    ppid: 'PSL-620893',
    serialNumber: '2422110742',
    status: 'success',
    changedAt: '2024-09-24T09:09:23.523Z',
    emailedAt: '2024-09-25T09:09:23.523Z',
  });

  await pcbSwapRepository.save([nullPcbSwap, completePcbSwap]);
};

describe('installer api', () => {
  jest.setTimeout(180_000);
  let mockServer: SetupServer;
  let environment: StartedDockerComposeEnvironment;
  let nestApi: INestApplication;

  beforeAll(async () => {
    environment = await new DockerComposeEnvironment(
      path.resolve(__dirname, '../../../../../libs/shared/test/db/experience'),
      'docker-compose.yml'
    )
      .withEnvironment({
        PORT: '5431',
        CONTAINER_NAME: 'installer-api-e2e',
      })
      .withWaitStrategy('installer-api-e2e', Wait.forListeningPorts())
      .withStartupTimeout(60000)
      .up();

    const configManager = await getAuth().projectConfigManager();

    jest.spyOn(configManager, 'getProjectConfig').mockResolvedValue({
      smsRegionConfig: {
        allowlistOnly: {
          allowedRegions: ['GB', 'FR', 'ES'],
        },
      },
      multiFactorConfig: undefined,
      recaptchaConfig: undefined,
      toJSON: jest.fn(),
    });

    mockServer = setupServer();
    mockServer.listen({ onUnhandledRequest: 'bypass' });

    nestApi = await bootstrap({
      module: AppModule,
      port: PORT,
      versioningOptions: {
        defaultVersion: API_VERSION,
        type: VersioningType.URI,
      },
      callback: (app, documentBuilder) => {
        useContainer(app.select(AppModule), { fallbackOnErrors: true });
        return documentBuilder;
      },
    });

    await seedDatabase(nestApi);
  });

  afterAll(async () => {
    await nestApi?.close();
    await mockServer?.close();
    await environment?.down();
  });

  describeHealthModule(BASE_URL_NEUTRAL);
  describeInstallsModule(BASE_URL_VERSIONED);
  describeInstallImagesModule(BASE_URL_VERSIONED);
  describeAccountModule(BASE_URL_VERSIONED);
  describePcbSwapsModule(BASE_URL_VERSIONED);
  describeAuthModule(BASE_URL_VERSIONED);
});
