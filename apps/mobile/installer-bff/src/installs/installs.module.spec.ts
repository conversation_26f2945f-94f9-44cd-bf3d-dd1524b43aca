import { OutOfServiceReasonKey } from '@experience/installer/types';
import { getTestFirebaseIdToken } from '../test.utils';
import axios from 'axios';
import crypto from 'crypto';

const USER_ID = 'wJKDBno8wgPV8XQacRKkUcVu1im3';

export const describeInstallsModule = (baseUrl: string) => {
  describe('installs module', () => {
    let userJwt;

    beforeEach(async () => {
      // Create a user profile to be used for creating installations
      userJwt = await getTestFirebaseIdToken(USER_ID);

      try {
        await axios.get(`${baseUrl}/account/profile`, {
          headers: { Authorization: `Bearer ${userJwt}` },
        });
      } catch {
        await axios.post(
          `${baseUrl}/account/profile`,
          {
            firstName: 'Test',
            lastName: 'User',
            phoneNumber: '**********',
            companyType: 'sole_trader',
            marketingConsent: true,
          },
          { headers: { Authorization: `Bearer ${userJwt}` } },
        );
      }
    });

    describe('installs controller', () => {
      it('should return an unauthorised error without token', async () => {
        await expect(
          axios.post(`${baseUrl}/installs/${crypto.randomUUID()}`, {
            chargerSettings: {},
          }),
        ).rejects.toThrow('Request failed with status code 401');
      });

      it('should return an 401 if user has not completed a profile', async () => {
        await expect(
          axios.post(
            `${baseUrl}/installs/${crypto.randomUUID()}`,
            {
              chargerSettings: {
                done: true,
              },
            },
            {
              headers: {
                Authorization: `Bearer ${await getTestFirebaseIdToken(
                  'userProfileNotCreated',
                )}`,
              },
            },
          ),
        ).rejects.toThrow('Request failed with status code 401');
      });

      it('should return a 400 if an invalid socket value is provided', async () => {
        await expect(
          axios.post(
            `${baseUrl}/installs/${crypto.randomUUID()}`,
            {
              chargerSettings: {
                deviceInformation: {
                  pslNumber: 'PSL-12345',
                  socket: 'invalid',
                },
                done: true,
              },
            },
            { headers: { Authorization: `Bearer ${userJwt}` } },
          ),
        ).rejects.toThrow('Request failed with status code 400');
      });

      it('should return a 400 if an invalid network interface value is provided', async () => {
        await expect(
          axios.post(
            `${baseUrl}/installs/${crypto.randomUUID()}`,
            {
              chargerSettings: {
                done: true,
                deviceInformation: {
                  pslNumber: 'PSL-12345',
                  socket: 'A',
                },
                connectivity: {
                  wifiConnectedState: 'Connected',
                  signalStrength: 0,
                  networkState: 'disconnected',
                  networkInterface: 'invalid',
                },
              },
            },
            { headers: { Authorization: `Bearer ${userJwt}` } },
          ),
        ).rejects.toThrow('Request failed with status code 400');
      });

      it('should return a 400 if an invalid network state value is provided', async () => {
        await expect(
          axios.post(
            `${baseUrl}/installs/${crypto.randomUUID()}`,
            {
              chargerSettings: {
                done: true,
                deviceInformation: {
                  pslNumber: 'PSL-12345',
                  socket: 'A',
                },
                connectivity: {
                  wifiConnectedState: 'Connected',
                  signalStrength: 0,
                  networkState: 'invalid',
                  networkInterface: 'ethernet',
                },
              },
            },
            { headers: { Authorization: `Bearer ${userJwt}` } },
          ),
        ).rejects.toThrow('Request failed with status code 400');
      });

      it('should return a 400 if other out of service reason added with no description', async () => {
        await expect(
          axios.post(
            `${baseUrl}/installs/${crypto.randomUUID()}`,
            {
              chargerSettings: {
                done: true,
                deviceInformation: {
                  pslNumber: 'PSL-12345',
                  socket: 'A',
                },
                deviceConfig: {
                  outOfService: true,
                  outOfServiceReasons: [
                    {
                      key: OutOfServiceReasonKey.OTHER,
                    },
                  ],
                },
              },
            },
            { headers: { Authorization: `Bearer ${userJwt}` } },
          ),
        ).rejects.toThrow('Request failed with status code 400');
      });

      it('should return a 400 if appointment is provided with no reference', async () => {
        await expect(
          axios.post(
            `${baseUrl}/installs/${crypto.randomUUID()}`,
            {
              chargerSettings: {
                deviceInformation: {
                  pslNumber: 'PSL-12345',
                },
                done: true,
              },
              appointment: {},
            },
            { headers: { Authorization: `Bearer ${userJwt}` } },
          ),
        ).rejects.toThrow('Request failed with status code 400');
      });

      it('should return a 400 if appointment is provided with empty reference', async () => {
        await expect(
          axios.post(
            `${baseUrl}/installs/${crypto.randomUUID()}`,
            {
              chargerSettings: {
                deviceInformation: {
                  pslNumber: 'PSL-12345',
                },
                done: true,
              },
              appointment: {
                reference: '',
              },
            },
            { headers: { Authorization: `Bearer ${userJwt}` } },
          ),
        ).rejects.toThrow('Request failed with status code 400');
      });

      it('should accept a new installation to be created from a pod point employee without a profile', async () => {
        const ppeJwt = await getTestFirebaseIdToken(
          'Gs0vQAEOzUh1ew9MYOgHygvdWzyh',
        );
        const options = { headers: { Authorization: `Bearer ${ppeJwt}` } };
        await expect(
          axios.get(`${baseUrl}/account/profile`, options),
        ).rejects.toThrow('Request failed with status code 404');
        const response = await axios.post(
          `${baseUrl}/installs/${crypto.randomUUID()}`,
          {
            chargerSettings: {
              deviceInformation: {
                pslNumber: 'PSL-12345',
              },
              done: true,
            },
          },
          options,
        );
        expect(response.status).toEqual(201);
        expect(response.data).toEqual({
          firstCompletedAt: null,
        });
        const userProfile = await axios.get(
          `${baseUrl}/account/profile`,
          options,
        );
        expect(userProfile.status).toEqual(200);
        expect(userProfile.data).toEqual({
          authId: 'Gs0vQAEOzUh1ew9MYOgHygvdWzyh',
          email: `<EMAIL>`,
          firstName: 'bob',
          lastName: 'dylan',
          phoneNumber: '+44 ************',
          companyName: 'Pod Point Ltd',
          companyNumber: '********',
          marketingConsent: true,
          companyType: 'company',
          installerType: 'POD_POINT',
        });
      });

      it('should accept a payload to create a new installation', async () => {
        const response = await axios.post(
          `${baseUrl}/installs/${crypto.randomUUID()}`,
          {
            chargerSettings: {
              deviceInformation: {
                pslNumber: 'PSL-12345',
              },
              done: true,
            },
          },
          { headers: { Authorization: `Bearer ${userJwt}` } },
        );
        expect(response.status).toEqual(201);
        expect(response.data).toEqual({
          firstCompletedAt: null,
        });
      });

      it('should accept a payload to create a new installation with an appointment', async () => {
        const response = await axios.post(
          `${baseUrl}/installs/${crypto.randomUUID()}`,
          {
            chargerSettings: {
              deviceInformation: {
                pslNumber: 'PSL-12345',
              },
              done: true,
            },
            appointment: {
              reference: '123456',
            },
          },
          { headers: { Authorization: `Bearer ${userJwt}` } },
        );
        expect(response.status).toEqual(201);
        expect(response.data).toEqual({
          firstCompletedAt: null,
        });
      });

      it('should accept a payload to update an installation', async () => {
        let response = await axios.post(
          `${baseUrl}/installs/${crypto.randomUUID()}`,
          {
            chargerSettings: {
              deviceInformation: {
                pslNumber: 'PSL-12345',
              },
              done: true,
            },
          },
          { headers: { Authorization: `Bearer ${userJwt}` } },
        );
        expect(response.status).toEqual(201);
        expect(response.data).toEqual({
          firstCompletedAt: null,
        });

        response = await axios.post(
          `${baseUrl}/installs/${crypto.randomUUID()}`,
          {
            chargerSettings: {
              deviceInformation: {
                pslNumber: 'PSL-12345',
                socket: 'A',
              },
              done: true,
            },
          },
          { headers: { Authorization: `Bearer ${userJwt}` } },
        );
        expect(response.status).toEqual(201);
        expect(response.data).toEqual({
          firstCompletedAt: null,
        });
      });

      it('should return the firstCompletedAt on the install with same PSL number', async () => {
        let response = await axios.post(
          `${baseUrl}/installs/${crypto.randomUUID()}`,
          {
            chargerSettings: {
              deviceInformation: {
                pslNumber: 'PCL-123456',
              },
              done: true,
            },
            completedAt: '2023-07-07T09:06:07Z',
          },
          { headers: { Authorization: `Bearer ${userJwt}` } },
        );
        expect(response.status).toEqual(201);
        expect(response.data).toEqual({
          firstCompletedAt: '2023-07-07T09:06:07.000Z',
        });

        response = await axios.post(
          `${baseUrl}/installs/${crypto.randomUUID()}`,
          {
            chargerSettings: {
              deviceInformation: {
                pslNumber: 'PCL-123456',
              },
              done: true,
            },
            completedAt: '2023-11-01T09:06:07Z',
          },
          { headers: { Authorization: `Bearer ${userJwt}` } },
        );

        expect(response.status).toEqual(201);
        expect(response.data).toEqual({
          firstCompletedAt: '2023-07-07T09:06:07.000Z',
        });
      });
    });
  });
};
