import './tailwind.css';
import '@fontsource-variable/quicksand';
import { AnalyticsLoader } from './components/analytics-loader/analytics-loader';
import { PublicEnvProvider } from 'next-runtime-env';

export const dynamic = 'force-dynamic';
export const metadata = {
  title: 'Pod Point',
  description: 'Pod Point Payments Webapp',
};

 
const Layout = async ({ children }: { children: React.ReactNode }) => (
  <html>
    <head>
      <PublicEnvProvider>
        <AnalyticsLoader />
      </PublicEnvProvider>
    </head>
    <body>
      <PublicEnvProvider>{children}</PublicEnvProvider>
    </body>
  </html>
);

export default Layout;
