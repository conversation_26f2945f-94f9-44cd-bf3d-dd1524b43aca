import { AcceptLanguageResolver, I18nModule, QueryResolver } from 'nestjs-i18n';
import { Analytics } from '@segment/analytics-node';
import { Api3TokenService } from '@experience/mobile/nest/api3-token';
import { AuthService } from '@experience/mobile/nest/auth-service';
import {
  AuthServiceSequelizeModule,
  Users as AuthServiceUsers,
  UserPreferences,
} from '@experience/shared/sequelize/auth-service';
import {
  Authorisers,
  BillingAccounts,
  PodUnitUser,
  PodadminSequelizeModule,
  TEST_USER_ENTITY,
} from '@experience/shared/sequelize/podadmin';
import {
  BadRequestException,
  ConflictException,
  InternalServerErrorException,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { CacheModule } from '@nestjs/cache-manager';
import { ConfigModule } from '@nestjs/config';
import {
  CreateUserResponseDto,
  ExtendedUserInfoResponseDto,
  TrackLoginRequest,
} from '@experience/mobile/driver-account/domain/user';
import { CustomerApi } from '@experience/billing-api/api-client';
import {
  EmailTheme,
  EmailThemeService,
  EmailVerificationService,
  PasswordResetService,
} from '@experience/mobile/nest/auth';
import {
  EventBridgeModule,
  EventBridgeService,
} from '@experience/shared/nest/aws/eventbridge-module';
import { FlagsService } from '@experience/shared/nest/remote-config';
import { LegacyAuthService } from '../legacy-auth/legacy-auth-service';
import { MigrateUserService } from '../migrate/migrate.user.service';
import { MockRepository } from '../migrate/migrate.service.spec';
import { OXRCurrencyConverter } from '@experience/shared/axios/currency-converter-client';
import { Op, Transaction } from 'sequelize';
import { PodAdminMigrateUserService } from '../migrate/pod-admin.migrate.user.service';
import { SimpleEmailService } from '@experience/shared/nest/aws/ses-module';
import { SubscriptionsApi } from '@experience/mobile/subscriptions-api/axios';
import { Test } from '@nestjs/testing';
import { TrackLoginService } from '@experience/mobile/nest/auth';
import { UserService } from './user.service';
import { Users } from '@experience/shared/sequelize/auth-service';
import { createMock } from '@golevelup/ts-jest';
import { generateToken } from '@experience/mobile/test/mocking';
import {
  getUser,
  mockApp,
  mockAuth,
  updateUser,
} from '@experience/mobile/test/mocking';
import { jest } from '@jest/globals';
import { mockDeep } from 'jest-mock-extended';
import axios from 'axios';

jest.mock('@experience/shared/nest/aws/ses-module');
jest.mock('@experience/shared/nest/aws/eventbridge-module');
jest.mock('@experience/shared/firebase/admin', () => ({
  getAuth: () => mockAuth,
  getApp: () => mockApp,
}));

const userRecord = {
  uid: '12345',
  email: '<EMAIL>',
  emailVerified: true,
  disabled: false,
  passwordHash: '12345',
  metadata: {
    creationTime: '2020-11-03T00:00:00',
    lastSignInTime: '2020-11-05T00:00:00',
  },
  providerData: [],
};

const aUser = {
  first_name: 'test',
  last_name: 'lastName',
  email: '<EMAIL>',
  locale: 'en',
};

const userWithConsent = {
  ...aUser,
  consent: {
    marketing: {
      isConsentGiven: 1,
      type: 'express',
      copy: 'I would like to receive updates about Pod Point products and services by email (and know that I can update my preferences from within any of the emails if I change my mind)',
      origin: 'opencharge-mobile-app',
    },
  },
  preferences: { unitOfDistance: 'mi' },
};

const authServiceUser = createMock<AuthServiceUsers>({
  id: 1,
  password: 'aPasswordHash',
  email: '<EMAIL>',
  uuid: 'fa95717e-cf98-4d2e-86cf-c12f580921f3',
  firstName: 'John',
  lastName: 'Doe',
  locale: 'en',
  deletedAt: null,
  userPreferences: [
    {
      key: 'unitOfDistance',
      value: 'mi',
    },
  ],
});

const podAdminUser = createMock<Users>({
  id: 512,
});

const returnedUser: ExtendedUserInfoResponseDto = {
  ...aUser,
  uid: '123',
  lastSignInTimestamp: new Date('2024-06-11Z00:00:00'),
  accountCreationTimestamp: new Date('2024-06-10Z00:00:00'),
  emailVerified: true,
  balance: { amount: 0, currency: 'GBP' },
  status: 'active',
  deletedAtTimestamp: null,
  preferences: {
    unitOfDistance: 'mi',
  },
};
const API3_CLIENT_TOKEN = generateToken('DRIVER_AUTH_PRIVATE_KEY', '30d');
describe('UserService', () => {
  let service: UserService;
  let authServiceUserRepository: MockRepository;
  let podAdminUserRepository: MockRepository;
  let billingAccountsRepository: MockRepository;
  let migrateUserService: MigrateUserService;
  let passwordResetService: PasswordResetService;
  let emailVerificationService: EmailVerificationService;
  let sesService: SimpleEmailService;
  let api3TokenService: Api3TokenService;
  let eventBridgeService: EventBridgeService;
  let trackLoginService: TrackLoginService;
  let legacyAuthService: LegacyAuthService;

  const logger = mockDeep<Logger>();
  const authServiceTransaction = mockDeep<Transaction>();
  const podAdminTransaction = mockDeep<Transaction>();
  const language = 'en';

  beforeEach(async () => {
    updateUser.mockImplementation(() => Promise.resolve(userRecord));
    getUser.mockImplementation(() => Promise.resolve(userRecord));
    const module = await Test.createTestingModule({
      imports: [
        I18nModule.forRoot({
          fallbackLanguage: 'en',
          loaderOptions: {
            path: './assets/driver-account-api/i18n/',
            watch: true,
          },
          resolvers: [
            { use: QueryResolver, options: ['lang'] },
            AcceptLanguageResolver,
          ],
        }),
        CacheModule.register(),
        ConfigModule,
        PodadminSequelizeModule,
        AuthServiceSequelizeModule,
        EventBridgeModule,
      ],
      providers: [
        UserService,
        MigrateUserService,
        PodAdminMigrateUserService,
        LegacyAuthService,
        {
          provide: 'AUTH_SERVICE_USERS_REPOSITORY',
          useValue: createMock<typeof AuthServiceUsers>(),
        },
        {
          provide: 'USERS_REPOSITORY',
          useValue: createMock<typeof Users>(),
        },
        {
          provide: 'BILLING_ACCOUNTS_REPOSITORY',
          useValue: createMock<typeof BillingAccounts>(),
        },
        {
          provide: 'AUTHORISERS_REPOSITORY',
          useValue: createMock<typeof Authorisers>(),
        },
        {
          provide: 'POD_UNIT_USER_REPOSITORY',
          useValue: createMock<PodUnitUser>(),
        },
        PasswordResetService,
        EmailVerificationService,
        SimpleEmailService,
        Api3TokenService,
        AuthService,
        {
          provide: OXRCurrencyConverter,
          useValue: createMock<OXRCurrencyConverter>(),
        },
        {
          provide: Analytics,
          useValue: createMock<Analytics>(),
        },
        EventBridgeService,
        TrackLoginService,
        {
          provide: CustomerApi,
          useValue: new CustomerApi(),
        },
        EmailThemeService,
        {
          provide: SubscriptionsApi,
          useValue: createMock<SubscriptionsApi>(),
        },
        {
          provide: FlagsService,
          useValue: createMock<FlagsService>(),
        },
      ],
    }).compile();
    module.useLogger(logger);

    authServiceUserRepository = module.get<MockRepository>(
      'AUTH_SERVICE_USERS_REPOSITORY',
    );
    podAdminUserRepository = module.get<MockRepository>('USERS_REPOSITORY');
    podAdminUserRepository.findOne.mockReturnValue(podAdminUser);
    billingAccountsRepository = module.get<MockRepository>(
      'BILLING_ACCOUNTS_REPOSITORY',
    );
    migrateUserService = module.get(MigrateUserService);
    legacyAuthService = module.get(LegacyAuthService);
    sesService = module.get<SimpleEmailService>(SimpleEmailService);

    authServiceUser.update.mockReturnValue(Promise.resolve(undefined));

    authServiceUserRepository.findOne.mockReturnValue(authServiceUser);
    authServiceUserRepository.findAll.mockReturnValue([authServiceUser]);
    billingAccountsRepository.findOne.mockReturnValue(
      createMock<BillingAccounts>({
        currency: 'GBP',
        balance: 100,
        paymentProcessorId: 'some-id',
      }),
    );
    jest.spyOn(migrateUserService, 'migrateUser').mockResolvedValue();
    jest
      .spyOn(migrateUserService, 'emailAlreadyExistsInAuth')
      .mockResolvedValue(false);
    jest
      .spyOn(migrateUserService, 'emailAlreadyExistsInPodAdmin')
      .mockResolvedValue(false);

    service = module.get<UserService>(UserService);
    passwordResetService =
      module.get<PasswordResetService>(PasswordResetService);
    emailVerificationService = module.get<EmailVerificationService>(
      EmailVerificationService,
    );
    api3TokenService = module.get<Api3TokenService>(Api3TokenService);
    eventBridgeService = module.get<EventBridgeService>(EventBridgeService);
    trackLoginService = module.get<TrackLoginService>(TrackLoginService);

    jest
      .spyOn(module.get(PodAdminMigrateUserService), 'getTransaction')
      .mockResolvedValue(podAdminTransaction);
  });

  describe('createUser', () => {
    it('should create user', async () => {
      jest.spyOn(axios, 'post').mockImplementation((url) => {
        if (
          url ===
          'http://test.vpce-svc-05be782203.eu-west-1.vpce.amazonaws.com/v5/users'
        ) {
          return Promise.resolve({ data: { ...api3Response } });
        }
      });

      await service.createUser(userWithConsent, language);

      expect(axios.post).toHaveBeenCalledWith(
        'http://test.vpce-svc-05be782203.eu-west-1.vpce.amazonaws.com/v5/users',
        userWithConsent,
      );

      expect(eventBridgeService.putEvent).toHaveBeenCalledWith(
        expect.objectContaining({
          eventBus: 'EVENT_BUS_ARN',
          source: 'driver-account-api',
          detailType: 'User.Created',
          detail: expect.objectContaining({
            specversion: '1.0',
            subject: expect.stringContaining('User.Created.Driver'),
            type: 'User.Created',
            source: '/experience/mobile/driver-account-api',
            data: {
              user: {
                id: api3Response.auth_id,
                email: userWithConsent.email,
                firstName: userWithConsent.first_name,
                lastName: userWithConsent.last_name,
                locale: userWithConsent.locale,
                consent: {
                  marketing: {
                    isConsentGiven:
                      userWithConsent.consent.marketing.isConsentGiven == 0
                        ? false
                        : true,
                    type: userWithConsent.consent.marketing.type,
                    copy: userWithConsent.consent.marketing.copy,
                    origin: userWithConsent.consent.marketing.origin,
                  },
                },
                preferences: {
                  unitOfDistance: userWithConsent.preferences.unitOfDistance,
                },
              },
            },
          }),
        }),
      );
    });

    it('should use default false marketing consent for user creation event if not populated', async () => {
      const userWithoutConsent = {
        ...userWithConsent,
        consent: { marketing: null },
      };

      jest.spyOn(axios, 'post').mockImplementation((url) => {
        if (
          url ===
          'http://test.vpce-svc-05be782203.eu-west-1.vpce.amazonaws.com/v5/users'
        ) {
          return Promise.resolve({ data: { ...api3Response } });
        }
      });

      await service.createUser(userWithoutConsent, language);

      expect(axios.post).toHaveBeenCalledWith(
        'http://test.vpce-svc-05be782203.eu-west-1.vpce.amazonaws.com/v5/users',
        userWithoutConsent,
      );

      expect(eventBridgeService.putEvent).toHaveBeenCalledWith(
        expect.objectContaining({
          eventBus: 'EVENT_BUS_ARN',
          source: 'driver-account-api',
          detailType: 'User.Created',
          detail: expect.objectContaining({
            specversion: '1.0',
            subject: expect.stringContaining('User.Created.Driver'),
            type: 'User.Created',
            source: '/experience/mobile/driver-account-api',
            data: {
              user: {
                id: api3Response.auth_id,
                email: userWithoutConsent.email,
                firstName: userWithoutConsent.first_name,
                lastName: userWithoutConsent.last_name,
                locale: userWithoutConsent.locale,
                consent: {
                  marketing: {
                    isConsentGiven: false,
                    type: null,
                    copy: null,
                    origin: null,
                  },
                },
                preferences: {
                  unitOfDistance: userWithoutConsent.preferences.unitOfDistance,
                },
              },
            },
          }),
        }),
      );
    });

    it('should log failure if user creation event cannot be sent', async () => {
      jest.spyOn(axios, 'post').mockImplementation((url) => {
        if (
          url ===
          'http://test.vpce-svc-05be782203.eu-west-1.vpce.amazonaws.com/v5/users'
        ) {
          return Promise.resolve({ data: { ...api3Response } });
        }
      });

      const error = new Error('Something went wrong');

      jest
        .spyOn(eventBridgeService, 'putEvent')
        .mockImplementationOnce(async () => {
          throw error;
        });

      await service.createUser(aUser, language);

      expect(axios.post).toHaveBeenCalledWith(
        'http://test.vpce-svc-05be782203.eu-west-1.vpce.amazonaws.com/v5/users',
        {
          email: '<EMAIL>',
          first_name: 'test',
          last_name: 'lastName',
          locale: 'en',
        },
      );

      expect(logger.error).toHaveBeenCalledWith(
        { error, authId: authServiceUser.uuid },
        'failed to submit user created event',
        UserService.name,
      );
    });

    it('should throw error if no auth id is present in auth response', async () => {
      jest.spyOn(axios, 'post').mockImplementation((url) => {
        if (
          url ===
          'http://test.vpce-svc-07469.eu-west-1.vpce.amazonaws.com/api/v1/users'
        ) {
          return Promise.resolve({ data: {} });
        } else if (
          url ===
          'http://test.vpce-svc-05be782203.eu-west-1.vpce.amazonaws.com/v5/users'
        ) {
          return Promise.resolve({ data: { ...api3Response, auth_id: null } });
        }
      });

      await expect(service.createUser(aUser, language)).rejects.toThrow(
        `Unexpected response when creating user ${aUser.email}. No auth id present.`,
      );
    });

    it('should rethrow error if auth create user errors', async () => {
      jest.spyOn(axios, 'post').mockRejectedValue(new Error('any error'));

      await expect(service.createUser(aUser, language)).rejects.toThrow(
        'any error',
      );
    });

    it('should log and rethrow an auth service error', async () => {
      jest.spyOn(axios, 'post').mockRejectedValue({
        response: {
          status: 422,
          statusText: 'Unprocessable Entity',
        },
      });
      try {
        await service.createUser(aUser, language);
      } catch {
        /* ignored testing logging */
      }

      expect(logger.error).toHaveBeenCalledWith(
        `User creation Error for ${aUser.email}`,
        { response: { status: 422, statusText: 'Unprocessable Entity' } },
        UserService.name,
      );
    });

    it('should return password reset link with redirect target, if no password is provided', async () => {
      jest.spyOn(axios, 'post').mockImplementation((url) => {
        if (
          url ===
          'http://test.vpce-svc-07469.eu-west-1.vpce.amazonaws.com/api/v1/users'
        ) {
          return Promise.resolve({ data: {} });
        } else if (
          url ===
          'http://test.vpce-svc-05be782203.eu-west-1.vpce.amazonaws.com/v5/users'
        ) {
          return Promise.resolve({
            data: { ...api3Response, password: undefined },
          });
        }
      });

      expect(
        await service.createUser(
          { ...aUser },
          language,
          'http://localhost:3000/login',
        ),
      ).toEqual({
        ...api3ResponseWithPasswordRemoved,
        password_reset_link:
          'https://identity-dev.pod-point.com/en/auth/action?redirect_url=http://localhost:3000/login',
      });
    });

    it('should return password reset link when no password is passed', async () => {
      jest
        .spyOn(passwordResetService, 'generateResetPasswordLink')
        .mockResolvedValue('aPasswordLink');

      jest
        .spyOn(axios, 'post')
        .mockImplementation(() =>
          Promise.resolve({ data: { ...api3Response } }),
        );
      jest
        .spyOn(emailVerificationService, 'sendEmailVerification')
        .mockResolvedValue();

      expect(await service.createUser({ ...aUser }, language)).toEqual({
        ...api3ResponseWithPasswordRemoved,
        password_reset_link: 'https://aPasswordLink',
      });
      expect(
        passwordResetService.generateResetPasswordLink,
      ).toHaveBeenCalledWith(
        aUser.email,
        'en',
        process.env.IDENTITY_BASE_URL_CLASSIC,
        undefined,
      );
    });

    it('should not return password reset link if password is provided', async () => {
      jest
        .spyOn(axios, 'post')
        .mockImplementation(() =>
          Promise.resolve({ data: { ...api3Response } }),
        );
      jest
        .spyOn(emailVerificationService, 'sendEmailVerification')
        .mockResolvedValue();

      expect(
        await service.createUser({ ...aUser, password: 'apassword' }, language),
      ).toEqual({
        ...api3ResponseWithPasswordRemoved,
      });
      expect(
        emailVerificationService.sendEmailVerification,
      ).toHaveBeenCalledWith(
        { email: api3Response.email },
        api3Response.locale,
        undefined,
      );
    });

    it('should throw conflict if user already exists in auth', async () => {
      jest
        .spyOn(migrateUserService, 'emailAlreadyExistsInAuth')
        .mockResolvedValue(true);

      await expect(service.createUser(aUser, language)).rejects.toThrow(
        'User already exists in auth',
      );
      expect(logger.error).toHaveBeenCalledWith(
        'User <EMAIL> already exists in auth',
        undefined,
        'UserService',
      );
    });

    it('should throw conflict if user already exists in podadmin', async () => {
      jest
        .spyOn(migrateUserService, 'emailAlreadyExistsInPodAdmin')
        .mockResolvedValue(true);

      await expect(service.createUser(aUser, language)).rejects.toThrow(
        'User already exists in podadmin',
      );

      expect(logger.error).toHaveBeenCalledWith(
        'User <EMAIL> already exists in podadmin',
        undefined,
        'UserService',
      );
    });

    it('should create user in gip service email verified false', async () => {
      jest
        .spyOn(axios, 'post')
        .mockImplementation(() =>
          Promise.resolve({ data: { ...api3Response } }),
        );

      await service.createUser(aUser, language);

      expect(axios.post).toHaveBeenCalledWith(
        'http://test.vpce-svc-05be782203.eu-west-1.vpce.amazonaws.com/v5/users',
        {
          email: '<EMAIL>',
          first_name: 'test',
          last_name: 'lastName',
          locale: 'en',
        },
      );

      expect(authServiceUserRepository.findOne).toHaveBeenCalledWith(
        expect.objectContaining({
          where: { uuid: 'fa95717e-cf98-4d2e-86cf-c12f580921f3' },
        }),
      );

      expect(migrateUserService.migrateUser).toHaveBeenCalledWith({
        email: '<EMAIL>',
        emailVerified: false,
        passwordHash: Buffer.from('aPasswordHash'),
        uid: 'fa95717e-cf98-4d2e-86cf-c12f580921f3',
        customClaims: {
          id: 1,
        },
      });
    });

    it('should create a gip link and return gip uid', async () => {
      jest.spyOn(axios, 'post').mockResolvedValueOnce({ data: api3Response });
      const response = await service.createUser(aUser, language);
      expect(mockAuth.generatePasswordResetLink).toHaveBeenCalledWith(
        '<EMAIL>',
        undefined,
      );
      expect(response).toEqual({
        ...api3ResponseWithPasswordRemoved,
        password_reset_link:
          'https://identity-dev.pod-point.com/en/auth/action',
      });
    });

    it('should not automatically verify email addresses if no password is supplied', async () => {
      jest.spyOn(axios, 'post').mockImplementation((url) => {
        if (
          url ===
          'http://test.vpce-svc-05be782203.eu-west-1.vpce.amazonaws.com/v5/users'
        ) {
          return Promise.resolve({
            data: { ...api3Response, password: undefined },
          });
        }
      });

      authServiceUserRepository.findOne.mockReturnValue({
        ...authServiceUser,
        password: undefined,
      });

      const fetchUserSpy = jest.spyOn(migrateUserService, 'fetchUserFromAuth');
      const migrateUserSpy = jest.spyOn(migrateUserService, 'migrateUser');

      await service.createUser(aUser, language);

      expect(fetchUserSpy).toHaveBeenCalledTimes(1);
      expect(fetchUserSpy).toHaveBeenCalledWith(authServiceUser.uuid, false);

      expect(migrateUserSpy).toHaveBeenCalledTimes(1);
      expect(migrateUserSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          passwordHash: undefined,
          emailVerified: false,
        }),
      );
    });
  });

  describe('getUser', () => {
    it('should get user by uid', async () => {
      const user = await service.getUser(
        'fa95717e-cf98-4d2e-86cf-c12f580921f3',
      );

      expect(authServiceUserRepository.findOne).toHaveBeenCalledWith({
        include: [
          {
            as: 'userPreferences',
            model: UserPreferences,
          },
        ],
        where: { uuid: 'fa95717e-cf98-4d2e-86cf-c12f580921f3' },
      });

      expect(billingAccountsRepository.findOne).toHaveBeenCalledWith({
        raw: true,
        where: { userId: 512 },
        transaction: undefined,
      });

      expect(getUser).toHaveBeenCalledWith(
        'fa95717e-cf98-4d2e-86cf-c12f580921f3',
      );

      expect(user).toEqual({
        email: '<EMAIL>',
        emailVerified: true,
        first_name: 'John',
        last_name: 'Doe',
        locale: 'en',
        uid: 'fa95717e-cf98-4d2e-86cf-c12f580921f3',
        status: 'active',
        balance: {
          amount: 100,
          currency: 'GBP',
        },
        lastSignInTimestamp: new Date(userRecord.metadata.lastSignInTime),
        accountCreationTimestamp: new Date(userRecord.metadata.creationTime),
        deletedAtTimestamp: null,
        paymentProcessorId: 'some-id',
        preferences: {
          unitOfDistance: 'mi',
        },
      });
    });
  });

  describe('updateUser', () => {
    it('should update an user first name, last name and locale', async () => {
      jest
        .spyOn(legacyAuthService, 'getTransaction')
        .mockResolvedValue(authServiceTransaction);
      const authUserToBeUpdated = createMock<Users>({
        uuid: '123',
        firstName: 'Johny',
        lastName: 'Cena',
        locale: 'ie',
        email: '<EMAIL>',
      });

      authServiceUserRepository.findOne.mockReturnValue(authUserToBeUpdated);
      const updated = await service.updateUser('123', {
        first_name: 'Johny',
        last_name: 'Cena',
        locale: 'ie',
      });

      expect(authServiceUserRepository.findOne).toHaveBeenCalledWith({
        where: { uuid: '123' },
        paranoid: true,
        transaction: authServiceTransaction,
      });

      expect(podAdminUserRepository.update).toHaveBeenCalledTimes(1);
      expect(podAdminUserRepository.update).toHaveBeenCalledWith(
        { firstName: 'Johny', lastName: 'Cena', locale: 'ie' },
        {
          where: { authId: '123' },
          paranoid: true,
          transaction: podAdminTransaction,
        },
      );

      expect(authUserToBeUpdated.update).toHaveBeenCalledTimes(1);
      expect(authUserToBeUpdated.update).toHaveBeenCalledWith(
        { firstName: 'Johny', lastName: 'Cena', locale: 'ie' },
        { transaction: authServiceTransaction },
      );
      expect(authServiceTransaction.commit).toHaveBeenCalledTimes(1);
      expect(podAdminTransaction.commit).toHaveBeenCalledTimes(1);
      expect(updated).toEqual({
        uid: '123',
        first_name: 'Johny',
        last_name: 'Cena',
        email: '<EMAIL>',
        locale: 'ie',
      });

      expect(eventBridgeService.putEvent).toHaveBeenCalledWith(
        expect.objectContaining({
          eventBus: 'EVENT_BUS_ARN',
          source: 'driver-account-api',
          detailType: 'User.Updated',
          detail: expect.objectContaining({
            data: {
              user: {
                id: authUserToBeUpdated.uuid,
                email: authUserToBeUpdated.email,
                firstName: 'Johny',
                lastName: 'Cena',
                locale: 'ie',
              },
            },
          }),
        }),
      );
    });

    it('should log an error if UserUpdatedEvent could not be raised', async () => {
      const error = new Error('Something went wrong');

      jest
        .spyOn(eventBridgeService, 'putEvent')
        .mockImplementationOnce(async () => {
          throw error;
        });

      jest
        .spyOn(legacyAuthService, 'getTransaction')
        .mockResolvedValue(authServiceTransaction);

      authServiceUserRepository.findOne.mockReturnValue(
        createMock<Users>({
          uuid: '123',
          firstName: 'Johny',
          lastName: 'Cena',
          locale: 'ie',
          email: '<EMAIL>',
        }),
      );

      await service.updateUser('123', {
        first_name: 'Johny',
        last_name: 'Cena',
        locale: 'ie',
      });

      expect(logger.error).toHaveBeenCalledWith(
        { error, authId: '123' },
        'failed to submit user updated event',
        UserService.name,
      );
    });

    it('should rollback when save to podadmin fails', async () => {
      jest
        .spyOn(legacyAuthService, 'getTransaction')
        .mockResolvedValue(authServiceTransaction);
      podAdminUserRepository.update.mockImplementation(() =>
        Promise.reject(new Error('There is a problem.')),
      );
      await expect(
        service.updateUser('123', {
          first_name: 'Johny',
          last_name: 'Cena',
          locale: 'ie',
        }),
      ).rejects.toThrow(new Error('There is a problem.'));

      expect(authServiceUserRepository.findOne).toHaveBeenCalledWith({
        where: { uuid: '123' },
        paranoid: true,
        transaction: authServiceTransaction,
      });

      expect(podAdminUserRepository.update).toHaveBeenCalledTimes(1);
      expect(podAdminUserRepository.update).toHaveBeenCalledWith(
        { firstName: 'Johny', lastName: 'Cena', locale: 'ie' },
        {
          where: { authId: '123' },
          paranoid: true,
          transaction: podAdminTransaction,
        },
      );

      expect(authServiceUser.update).toHaveBeenCalledTimes(1);
      expect(authServiceUser.update).toHaveBeenCalledWith(
        { firstName: 'Johny', lastName: 'Cena', locale: 'ie' },
        { transaction: authServiceTransaction },
      );
      expect(authServiceTransaction.rollback).toHaveBeenCalledTimes(1);
      expect(podAdminTransaction.commit).toHaveBeenCalledTimes(0);
    });

    it('should not try and save to podadmin when save to auth service fails', async () => {
      jest
        .spyOn(legacyAuthService, 'getTransaction')
        .mockResolvedValue(authServiceTransaction);
      authServiceUser.update.mockImplementation(() =>
        Promise.reject(new Error('There is a problem.')),
      );
      await expect(
        service.updateUser('123', {
          first_name: 'Johny',
          last_name: 'Cena',
          locale: 'ie',
        }),
      ).rejects.toThrow(new Error('There is a problem.'));

      expect(authServiceUserRepository.findOne).toHaveBeenCalledWith({
        where: { uuid: '123' },
        paranoid: true,
        transaction: authServiceTransaction,
      });

      expect(authServiceUser.update).toHaveBeenCalledTimes(1);
      expect(authServiceUser.update).toHaveBeenCalledWith(
        { firstName: 'Johny', lastName: 'Cena', locale: 'ie' },
        { transaction: authServiceTransaction },
      );
      expect(podAdminUserRepository.update).toHaveBeenCalledTimes(0);
      expect(authServiceTransaction.rollback).toHaveBeenCalledTimes(1);
    });

    it('should not call any updates when the transaction is not returned successfully ', async () => {
      jest
        .spyOn(legacyAuthService, 'getTransaction')
        .mockRejectedValue(
          new BadRequestException('There was a problem updating the user.'),
        );
      await expect(
        service.updateUser('123', {
          first_name: 'Johny',
          last_name: 'Cena',
          locale: 'ie',
        }),
      ).rejects.toThrow(
        new BadRequestException('There was a problem updating the user.'),
      );

      expect(authServiceUserRepository.update).toHaveBeenCalledTimes(0);
      expect(podAdminUserRepository.update).toHaveBeenCalledTimes(0);
      expect(authServiceTransaction.rollback).toHaveBeenCalledTimes(0);
    });
  });

  describe('enableUser', () => {
    it('should throw a ConflictException when there is a user but is not disabled', async () => {
      jest.spyOn(service, 'getUser').mockResolvedValueOnce(returnedUser);
      await expect(service.enableUser('123')).rejects.toThrow(
        new ConflictException('The user is not disabled'),
      );
    });

    it('should enable a user', async () => {
      jest
        .spyOn(service, 'getUser')
        .mockResolvedValueOnce({ ...returnedUser, status: 'disabled' });
      jest
        .spyOn(migrateUserService, 'enableUser')
        .mockResolvedValueOnce(authServiceUser);
      jest
        .spyOn(migrateUserService, 'enableUserInFirebase')
        .mockResolvedValueOnce();
      const response = await service.enableUser('123');
      expect(response).toEqual({
        first_name: authServiceUser.firstName,
        last_name: authServiceUser.lastName,
        locale: authServiceUser.locale,
        uid: authServiceUser.uuid,
        email: authServiceUser.email,
      });
      expect(migrateUserService.enableUser).toHaveBeenCalledTimes(1);
      expect(eventBridgeService.putEvent).toHaveBeenCalledWith(
        expect.objectContaining({
          eventBus: 'EVENT_BUS_ARN',
          source: 'driver-account-api',
          detailType: 'User.Restored',
          detail: expect.objectContaining({
            specversion: '1.0',
            subject: expect.stringContaining('User.Restored.Driver'),
            type: 'User.Restored',
            source: '/experience/mobile/driver-account-api',
            data: {
              user: {
                id: returnedUser.uid,
                email: returnedUser.email,
              },
            },
          }),
        }),
      );
    });
  });

  describe('getByFilter', () => {
    it('should get users filtered by email (non fuzzy)', async () => {
      authServiceUserRepository.findAll.mockReturnValue([
        {
          id: 1,
          password: 'aPasswordHash',
          email: '<EMAIL>',
          uuid: 'fa95717e-cf98-4d2e-86cf-c12f580921f3',
          firstName: 'John',
          lastName: 'Doe',
          locale: 'en',
        },
      ]);

      const users = await service.getByFilter({ email: '<EMAIL>' });

      expect(authServiceUserRepository.findAll).toHaveBeenCalledWith({
        limit: 10,
        raw: true,
        where: { email: { [Op['eq']]: '<EMAIL>' } },
        order: [['email', 'ASC']],
      });
      expect(users).toEqual([
        expect.objectContaining({
          first_name: 'John',
          last_name: 'Doe',
          locale: 'en',
        }),
      ]);
    });

    it('should get users filtered by email (fuzzy)', async () => {
      authServiceUserRepository.findAll.mockReturnValue([
        {
          id: 1,
          password: 'aPasswordHash',
          email: '<EMAIL>',
          uuid: 'fa95717e-cf98-4d2e-86cf-c12f580921f3',
          firstName: 'John',
          lastName: 'Doe',
          locale: 'en',
        },
      ]);

      const users = await service.getByFilter({ emailLike: '<EMAIL>' });

      expect(authServiceUserRepository.findAll).toHaveBeenCalledWith({
        limit: 10,
        raw: true,
        where: { email: { [Op['like']]: '<EMAIL>%' } },
        order: [['email', 'ASC']],
      });
      expect(users).toEqual([
        expect.objectContaining({
          first_name: 'John',
          last_name: 'Doe',
          locale: 'en',
        }),
      ]);
    });

    it("should return empty when email doesn't match", async () => {
      authServiceUserRepository.findAll.mockReturnValue([]);

      const users = await service.getByFilter({ email: '<EMAIL>' });

      expect(authServiceUserRepository.findAll).toHaveBeenCalledWith({
        limit: 10,
        raw: true,
        where: { email: { [Op['eq']]: '<EMAIL>' } },
        order: [['email', 'ASC']],
      });
      expect(users).toEqual([]);
    });

    it('should get users filtered by ppid', async () => {
      jest
        .spyOn(podAdminUserRepository, 'findAll')
        .mockReturnValue([TEST_USER_ENTITY]);

      const users = await service.getByFilter({ ppid: 'PSL-12345' });

      expect(podAdminUserRepository.findAll).toHaveBeenCalledWith({
        include: [
          {
            as: 'podUnitUsers',
            include: [
              {
                as: 'unit',
                model: expect.any(Function),
              },
            ],
            model: expect.any(Function),
          },
        ],
        where: {
          '$podUnitUsers.unit.ppid$': 'PSL-12345',
        },
      });

      expect(authServiceUserRepository.findAll).toHaveBeenCalledWith({
        limit: 10,
        raw: true,
        where: { uuid: { [Op.in]: [TEST_USER_ENTITY.authId] } },
        order: [['email', 'ASC']],
      });

      expect(users).toEqual([
        {
          email: '<EMAIL>',
          first_name: 'John',
          last_name: 'Doe',
          locale: 'en',
          uid: 'fa95717e-cf98-4d2e-86cf-c12f580921f3',
        },
      ]);
    });

    it('should return an empty array if there are no users found for a given ppid', async () => {
      jest.spyOn(podAdminUserRepository, 'findAll').mockReturnValue([]);
      jest.spyOn(authServiceUserRepository, 'findAll').mockReturnValue([]);

      const users = await service.getByFilter({ ppid: 'PSL-12345' });

      expect(users).toEqual([]);
    });

    it('should return empty if no email is passed or email is empty', async () => {
      authServiceUserRepository.findAll.mockReturnValue([]);

      const users = await service.getByFilter({});

      expect(users).toEqual([]);
    });
  });

  describe('getSuppressedStatus()', () => {
    it('gets the user from Firebase Authentication', async () => {
      getUser.mockReturnValue({ email: '<EMAIL>' });

      await service.getSuppressedStatus('fa95717e-cf98-4d2e-86cf-c12f580921f3');

      expect(getUser).toHaveBeenCalledWith(
        'fa95717e-cf98-4d2e-86cf-c12f580921f3',
      );
    });

    it("calls the SES Service with the user's email address", async () => {
      const sesSpy = jest
        .spyOn(sesService, 'getSuppressedDestination')
        .mockReturnValue(Promise.resolve('BOUNCE'));

      await service.getSuppressedStatus('fa95717e-cf98-4d2e-86cf-c12f580921f3');

      expect(sesSpy).toHaveBeenCalledTimes(1);
      expect(sesSpy).toHaveBeenCalledWith(userRecord.email);
    });

    it('throws a NotFoundException if the user does not exist', async () => {
      getUser.mockImplementation(() => {
        throw new (class MockFirebaseError {
          code = 'auth/user-not-found';
        })();
      });

      await expect(
        service.getSuppressedStatus('fa95717e-cf98-4d2e-86cf-c12f580921f3'),
      ).rejects.toThrow(NotFoundException);
    });

    it('throws an InternalServerErrorException if something goes wrong', async () => {
      jest
        .spyOn(sesService, 'getSuppressedDestination')
        .mockImplementation(() => {
          throw new Error();
        });

      await expect(
        service.getSuppressedStatus('fa95717e-cf98-4d2e-86cf-c12f580921f3'),
      ).rejects.toThrow(InternalServerErrorException);
    });
  });

  describe('removeSuppressionStatus', () => {
    it('gets the user from Firebase Authentication', async () => {
      getUser.mockReturnValue({ email: '<EMAIL>' });

      await service.removeSuppressionStatus(
        'fa95717e-cf98-4d2e-86cf-c12f580921f3',
      );

      expect(getUser).toHaveBeenCalledWith(
        'fa95717e-cf98-4d2e-86cf-c12f580921f3',
      );
    });

    it("calls the SES Service with the user's email address", async () => {
      const sesSpy = jest
        .spyOn(sesService, 'deleteSuppressedDestination')
        .mockResolvedValue();

      await service.removeSuppressionStatus(
        'fa95717e-cf98-4d2e-86cf-c12f580921f3',
      );

      expect(sesSpy).toHaveBeenCalledTimes(1);
      expect(sesSpy).toHaveBeenCalledWith(userRecord.email);
    });

    it('throws a NotFoundException if the user does not exist', async () => {
      getUser.mockImplementation(() => {
        throw new (class MockFirebaseError {
          code = 'auth/user-not-found';
        })();
      });

      await expect(
        service.removeSuppressionStatus('fa95717e-cf98-4d2e-86cf-c12f580921f3'),
      ).rejects.toThrow(NotFoundException);
    });

    it('throws an InternalServerErrorException if something goes wrong', async () => {
      jest
        .spyOn(sesService, 'deleteSuppressedDestination')
        .mockImplementation(() => {
          throw new Error();
        });

      await expect(
        service.removeSuppressionStatus('fa95717e-cf98-4d2e-86cf-c12f580921f3'),
      ).rejects.toThrow(InternalServerErrorException);
    });
  });

  describe('deleteUser', () => {
    beforeEach(() => {
      jest.spyOn(axios, 'delete').mockResolvedValue({ data: {} });
      jest
        .spyOn(api3TokenService, 'refreshServiceToServiceToken')
        .mockResolvedValue(API3_CLIENT_TOKEN);
    });

    it.each([
      ['no balance', { currency: 'GBP', balance: 0 }],
      [
        'negative balance within threshold',
        { currency: 'GBP', balance: -29.9 },
      ],
    ])(
      'without force should delete user with %s',
      async (_scenario, balance) => {
        billingAccountsRepository.findOne.mockReturnValue(
          createMock<BillingAccounts>(balance),
        );

        await service.deleteUser('123', false);

        expect(axios.delete).toHaveBeenCalledWith(
          `${process.env.AUTH_SERVICE_USER_URL}/123`,
          { headers: { Authorization: `Bearer ${API3_CLIENT_TOKEN}` } },
        );
      },
    );

    it.each([
      ['negative balance beyond threshold', { currency: 'GBP', balance: -30 }],
      ['positive balance', { currency: 'GBP', balance: 1 }],
    ])(
      'without force should not delete user with %s',
      async (_scenario, balance) => {
        billingAccountsRepository.findOne.mockReturnValue(
          createMock<BillingAccounts>(balance),
        );

        await expect(service.deleteUser('123', false)).rejects.toThrow(
          new ConflictException(
            'Failed to delete because of outstanding balance in the billing account',
          ),
        );
      },
    );

    it.each([
      ['no balance', { currency: 'GBP', balance: 0 }],
      ['negative balance within threshold', { currency: 'GBP', balance: -299 }],
      ['negative balance beyond threshold', { currency: 'GBP', balance: -300 }],
      ['positive balance', { currency: 'GBP', balance: 1 }],
    ])('with force should delete user with %s', async (_scenario, balance) => {
      billingAccountsRepository.findOne.mockReturnValue(
        createMock<BillingAccounts>(balance),
      );

      await service.deleteUser('123', true);

      expect(axios.delete).toHaveBeenCalledWith(
        `${process.env.AUTH_SERVICE_USER_URL}/123`,
        { headers: { Authorization: `Bearer ${API3_CLIENT_TOKEN}` } },
      );
    });

    it('should raise a user deleted event when delete successful', async () => {
      jest.spyOn(service, 'getUser').mockResolvedValueOnce(returnedUser);

      billingAccountsRepository.findOne.mockReturnValue(
        createMock<BillingAccounts>({ currency: 'GBP', balance: 0 }),
      );

      await service.deleteUser('123', false);

      expect(eventBridgeService.putEvent).toHaveBeenCalledWith(
        expect.objectContaining({
          eventBus: 'EVENT_BUS_ARN',
          source: 'driver-account-api',
          detailType: 'User.Deleted',
          detail: expect.objectContaining({
            specversion: '1.0',
            subject: expect.stringContaining('User.Deleted.Driver'),
            type: 'User.Deleted',
            source: '/experience/mobile/driver-account-api',
            data: {
              user: {
                id: returnedUser.uid,
                email: returnedUser.email,
              },
            },
          }),
        }),
      );
    });

    it('should log an error when user deleted event can not be raised', async () => {
      billingAccountsRepository.findOne.mockReturnValue(
        createMock<BillingAccounts>({ currency: 'GBP', balance: 0 }),
      );

      const error = new Error('Something went wrong');

      jest
        .spyOn(eventBridgeService, 'putEvent')
        .mockImplementationOnce(async () => {
          throw error;
        });

      await service.deleteUser('123', false);

      expect(logger.error).toHaveBeenCalledWith(
        { error, authId: authServiceUser.uuid },
        'failed to submit user deleted event',
        UserService.name,
      );
    });
  });

  describe('trackLogin', () => {
    const customUser: ExtendedUserInfoResponseDto = {
      email: '<EMAIL>',
      emailVerified: true,
      first_name: 'John',
      last_name: 'Doe',
      locale: 'en',
      uid: 'fa95717e-cf98-4d2e-86cf-c12f580921f3',
      status: 'active',
      balance: {
        amount: 100,
        currency: 'GBP',
      },
      lastSignInTimestamp: new Date(userRecord.metadata.lastSignInTime),
      accountCreationTimestamp: new Date(userRecord.metadata.creationTime),
      deletedAtTimestamp: null,
      paymentProcessorId: 'some-id',
    };
    const mockedtrackLoginRequest: TrackLoginRequest = {
      ipAddress: '2a00:23ee:1320:109f:98e0:dcc6:b917:f126',
      userAgent:
        'FirebaseAuth.iOS/8.15.0 com.podpoint.podpoint/3.26.0 iPhone/17.5.1 hw/iPhone14_5,gzip(gfe),gzip(gfe',
      timestamp: '2020-03-14 00:00:00',
      authId: userRecord.uid,
    };
    beforeEach(() => {
      jest.spyOn(trackLoginService, 'sendEmail').mockResolvedValue();
    });

    it('should send an email', async () => {
      jest.spyOn(service, 'getUser').mockResolvedValueOnce(customUser);
      await service.trackLogin(userRecord.uid, mockedtrackLoginRequest);
      expect(trackLoginService.sendEmail).toHaveBeenCalledTimes(1);
      expect(trackLoginService.sendEmail).toHaveBeenCalledWith(
        customUser,
        mockedtrackLoginRequest,
        EmailTheme.DEFAULT,
      );
    });

    it('should not send an email when getUser fails', async () => {
      const error = new Error('an error');
      jest
        .spyOn(service, 'getUser')
        .mockImplementationOnce(() => Promise.reject(error));
      await expect(
        service.trackLogin(userRecord.uid, mockedtrackLoginRequest),
      ).rejects.toThrow(error);
      expect(trackLoginService.sendEmail).toHaveBeenCalledTimes(0);
    });
  });
});

const api3Response: CreateUserResponseDto = {
  email: '<EMAIL>',
  first_name: 'murrayhking',
  last_name: 'sdfasd',
  locale: 'en',
  password: 'passwordhash',
  consent: {
    marketing: {
      isConsentGiven: 0,
      type: 'express',
      copy: 'I would like to receive updates about Pod Point products and services by email (and know that I can update my preferences from within any of the emails if I change my mind)',
      origin: 'opencharge-mobile-app',
    },
  },
  preferences: { unitOfDistance: 'mi' },
  auth_id: 'fa95717e-cf98-4d2e-86cf-c12f580921f3',
  created_at: '2023-10-02 12:27:19',
  id: '910004932',
};

const { password, ...api3ResponseWithPasswordRemoved } = api3Response;
