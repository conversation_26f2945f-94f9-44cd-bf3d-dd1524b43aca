// namespace using domain name as recommended by:
//   https://auth0.com/docs/secure/tokens/json-web-tokens/create-custom-claims#namespaced-guidelines
export const CUSTOM_CLAIMS_NAMESPACE = 'https://pod-point.com/oauth';

// it doesn't seem possible to combine in-line docs, and keys based on template
// literal type maps.
// so use an example object and have typescript infer the type.
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const customClaimsTemplate = {
  // public claims (defined in oauth 2.0 token exchange)
  // see: https://datatracker.ietf.org/doc/html/rfc8693#name-json-web-token-claims-and-i
  /** client id of the oauth application this token was issued to */
  client_id: 'string' as string,
  /** space-separated scopes available using this token */
  scope: 'string' as string,
  // private claims
  /** if true, this token is issued for use by a third-party (e.g. not the mobile app) */
  [`${CUSTOM_CLAIMS_NAMESPACE}/third_party`]: true as boolean,
  /** nonce used for validation */
  [`${CUSTOM_CLAIMS_NAMESPACE}/nonce`]: 'string' as string,
};
export type OAuthCustomClaims = Partial<typeof customClaimsTemplate>;

export const isOAuthCustomClaims = (obj: unknown): obj is OAuthCustomClaims => {
  if (typeof obj !== 'object' || !obj) {
    return false;
  }
  const claims: OAuthCustomClaims = obj as OAuthCustomClaims;
  return (
    typeof claims.scope === 'string' &&
    typeof claims.client_id === 'string' &&
    ['undefined', 'boolean'].includes(
      typeof claims[`${CUSTOM_CLAIMS_NAMESPACE}/third_party`],
    ) &&
    ['undefined', 'string'].includes(
      typeof claims[`${CUSTOM_CLAIMS_NAMESPACE}/nonce`],
    )
  );
};
