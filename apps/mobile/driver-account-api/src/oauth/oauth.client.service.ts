import { Inject, Injectable, Logger } from '@nestjs/common';
import { OAUTH_APPLICATION_REPOSITORY } from '@experience/mobile/driver-account/database';
import { OAuthApplication } from '@experience/mobile/driver-account/database';
import { Repository } from 'typeorm';
import { validate as isUuid } from 'uuid';

@Injectable()
export class OAuthClientService {
  private readonly logger = new Logger(OAuthClientService.name);

  constructor(
    @Inject(OAUTH_APPLICATION_REPOSITORY)
    private readonly oauthApplicationRepository: Repository<OAuthApplication>,
  ) {}

  async getClient(params: {
    clientId: string;
  }): Promise<(OAuthApplication & { clientId: string }) | null> {
    // it is unusual for an oauth client id to be a uuid, but it is convenient
    // to use the optimised uuid postgres primary key type. Therefore, we convert
    // the uuid to base64 for use in the client id.

    const clientUuid = this.base64ToUuid(params.clientId);
    if (typeof clientUuid !== 'string') {
      this.logger.error({ params }, 'Invalid client ID');
      return null;
    }

    const client: OAuthApplication =
      await this.oauthApplicationRepository.findOne({
        where: {
          id: clientUuid,
        },
        relations: ['allowedScopes'],
      });

    if (!client) {
      this.logger.warn({ params }, 'Client not found');
      return null;
    }
    const clientId = this.uuidToBase64(client.id);
    if (params.clientId !== clientId) {
      this.logger.error({ params, client, clientId }, 'Client ID mismatch');
      return null;
    }

    return {
      ...client,
      clientId,
    };
  }

  private base64ToUuid = (base64: string): string | null => {
    try {
      const hex = Buffer.from(base64, 'base64url').toString('hex');
      const uuid = [
        hex.slice(0, 8),
        hex.slice(8, 12),
        hex.slice(12, 16),
        hex.slice(16, 20),
        hex.slice(20),
      ].join('-');
      return isUuid(uuid) ? uuid : null;
    } catch {
      return null;
    }
  };

  private uuidToBase64 = (uuid: string): string | null =>
    isUuid(uuid)
      ? Buffer.from(uuid.replaceAll('-', ''), 'hex').toString('base64url')
      : null;
}
