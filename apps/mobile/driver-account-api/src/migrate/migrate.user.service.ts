import { Injectable, Logger } from '@nestjs/common';
import { UserImportRecord } from 'firebase-admin/lib/auth';
import { auth } from 'firebase-admin';
import { getAuth } from '@experience/shared/firebase/admin';
import UserImportResult = auth.UserImportResult;
import { UserEmailDto } from '@experience/mobile/driver-account/domain/user';
import { UsersAttributes } from '@experience/shared/sequelize/auth-service';
import { WhereOptions } from 'sequelize';
import UpdateRequest = auth.UpdateRequest;
import { Currencies } from '@experience/shared/typescript/utils';
import { CustomerApi } from '@experience/billing-api/api-client';
import { LegacyAuthService } from '../legacy-auth/legacy-auth-service';
import { PodAdminMigrateUserService } from './pod-admin.migrate.user.service';

@Injectable()
export class MigrateUserService {
  private readonly logger = new Logger(MigrateUserService.name);

  constructor(
    private readonly podAdminMigrateUserService: PodAdminMigrateUserService,
    private readonly legacyAuthService: LegacyAuthService,
    private readonly customerApi: CustomerApi,
  ) {}

  public async migrateUser(userToBeMigrated: UserImportRecord) {
    const result: UserImportResult = await getAuth().importUsers(
      [userToBeMigrated],
      {
        hash: {
          algorithm: 'BCRYPT',
          rounds: 10,
        },
      },
    );

    if (result.errors.length === 0) {
      this.logger.log(
        `successfully migrated user ${userToBeMigrated.email} with email verification ${userToBeMigrated.emailVerified}.`,
      );
    }
  }

  public async enableUserInFirebase(uuid: string) {
    await this.updateUserStatusInFirebase(uuid, false);
  }

  public async disableUserInFirebase(uuid: string) {
    await this.updateUserStatusInFirebase(uuid, true);
  }

  public async updateUserStatusInFirebase(uuid: string, disabled: boolean) {
    try {
      await getAuth().getUser(uuid);
    } catch (error) {
      if (error.code === 'auth/user-not-found') {
        this.logger.log(
          `ignored disable user as not found gip user pool uuid: ${uuid}`,
        );
        return;
      }
    }

    await getAuth().updateUser(uuid, {
      disabled,
    });

    this.logger.log(
      `successfully updated user with uuid: ${uuid} and disabled status ${disabled}`,
    );
  }

  public async fetchUserFromAuth(
    uuid: string,
    emailVerified: boolean,
  ): Promise<UserImportRecord> {
    const user = await this.legacyAuthService.fetchUser(uuid);

    return {
      uid: user.uuid,
      email: user.email,
      emailVerified,
      passwordHash: user.password ? Buffer.from(user.password) : undefined,
      customClaims: { id: user.id },
    };
  }

  public async fetchUsers(where?: WhereOptions<UsersAttributes>, limit = 10) {
    return await this.legacyAuthService.fetchUsers(where, limit);
  }

  public async updateEmail({ newEmail, oldEmail }: UserEmailDto) {
    if (await this.checkIfUserExists(newEmail)) {
      return false;
    }

    const user = await getAuth().getUserByEmail(oldEmail);

    const transaction = await this.legacyAuthService.getTransaction();
    try {
      await this.legacyAuthService.updateUserEmail(
        oldEmail,
        newEmail,
        transaction,
      );
      await this.updateInGIPAndPodAdmin({ newEmail, oldEmail });

      await transaction.commit();
    } catch (error) {
      this.logger.error(error);
      await transaction.rollback();
      throw error;
    }

    try {
      this.logger.log({ userId: user.uid }, "updating user's stripe customer");

      await this.customerApi.customerControllerUpdateCustomer(user.uid, {
        email: newEmail,
      });
    } catch (error) {
      this.logger.error(
        { userId: user.uid, error },
        'failed to update customer in stripe',
      );
    }

    return true;
  }

  private async updateInGIPAndPodAdmin({ newEmail, oldEmail }: UserEmailDto) {
    await this.updateInGIP(oldEmail, { email: newEmail, emailVerified: true });
    try {
      await this.podAdminMigrateUserService.updateUserEmail({
        newEmail,
        oldEmail,
      });
    } catch (error) {
      this.logger.error(error);
      await this.updateInGIP(newEmail, { email: oldEmail });
      throw error;
    }
  }

  public async updateUser(
    uid: string,
    attributes: Pick<UsersAttributes, 'firstName' | 'lastName' | 'locale'>,
  ) {
    return await this.updateUserInDB(uid, attributes);
  }

  public async enableUser(uid: string, deletedAt: Date | null) {
    let user = await this.enableUserinDatabase(uid);

    try {
      await this.enableUserInFirebase(uid);
    } catch (error) {
      this.logger.error({ error }, 'there was an error enabling user');

      user = await this.disableUserInDatabase(uid, deletedAt);
    }

    return user;
  }

  async enableUserinDatabase(uid: string) {
    const transaction = await this.legacyAuthService.getTransaction();

    try {
      const authServiceUser = await this.legacyAuthService.getAuthUser(
        uid,
        false,
        transaction,
      );
      await authServiceUser.update({ deletedAt: null }, { transaction });

      await this.podAdminMigrateUserService.enableUser(uid);

      await transaction.commit();
    } catch (error) {
      this.logger.error(error);
      await transaction.rollback();
      throw error;
    }

    return this.legacyAuthService.fetchUser(uid);
  }

  async disableUserInDatabase(uid: string, deletedAt: Date) {
    const transaction = await this.legacyAuthService.getTransaction();

    try {
      const authServiceUser = await this.legacyAuthService.getAuthUser(
        uid,
        false,
        transaction,
      );
      await authServiceUser.update({ deletedAt }, { transaction });

      await this.podAdminMigrateUserService.disableUser(uid, deletedAt);

      await transaction.commit();
    } catch (error) {
      this.logger.error(error);
      await transaction.rollback();
      throw error;
    }

    return this.legacyAuthService.fetchUser(uid);
  }

  async updateUserInDB(
    uid: string,
    attributes: Pick<UsersAttributes, 'firstName' | 'lastName' | 'locale'>,
    paranoid = true,
  ) {
    const transaction = await this.legacyAuthService.getTransaction();

    try {
      const authServiceUser = await this.legacyAuthService.getAuthUser(
        uid,
        paranoid,
        transaction,
      );
      const oldLocale = authServiceUser.locale.toLowerCase();
      const newLocale = attributes['locale']?.toLowerCase();
      const newCurrency =
        oldLocale !== newLocale ? Currencies[newLocale] : undefined;

      await authServiceUser.update(attributes, { transaction });

      await this.podAdminMigrateUserService.updateUser(
        uid,
        attributes,
        newCurrency,
        paranoid,
      );

      await transaction.commit();
    } catch (error) {
      this.logger.error(error);
      await transaction.rollback();
      throw error;
    }

    return this.fetchUser(uid);
  }
  public async emailAlreadyExistsInPodAdmin(email: string): Promise<boolean> {
    return this.podAdminMigrateUserService.emailAlreadyExistsInPodAdmin(email);
  }

  async fetchUserWithPreferences(uuid: string) {
    return await this.legacyAuthService.fetchUserWithPreferences(uuid);
  }

  public async fetchUser(uuid: string) {
    return await this.legacyAuthService.fetchUser(uuid);
  }

  async emailAlreadyExistsInAuth(email: string) {
    return await this.legacyAuthService.emailAlreadyExistsInAuth(email);
  }

  async updateInGIP(oldEmail: string, updateRequest: UpdateRequest) {
    const auth = getAuth();
    const userRecord = await auth.getUserByEmail(oldEmail);
    await auth.updateUser(userRecord.uid, updateRequest);
  }

  async checkIfUserExists(email: string) {
    const auth = getAuth();
    try {
      await auth.getUserByEmail(email);
      return true;
    } catch {
      this.logger.log(`User with ${email} was not found`);
      return false;
    }
  }

  async updateBillingAccountIfNeeded(uid: string) {
    const authUser = await this.fetchUser(uid);
    const currency = Currencies[authUser.locale];
    const billingAccount =
      await this.podAdminMigrateUserService.getBillingAccountByAuthId(uid);

    if (billingAccount && billingAccount.currency !== currency) {
      await this.podAdminMigrateUserService.updateUserBalance({
        uid,
        currency,
        authId: uid,
      });
    }
  }
}
