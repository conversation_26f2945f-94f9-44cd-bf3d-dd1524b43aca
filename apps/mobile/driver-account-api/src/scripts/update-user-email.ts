import {
  CONNECTION_NAME as AUTH_SERVICE_CONNECTION_NAME,
  AuthServiceSequelizeModule,
  Users as AuthServiceUsers,
} from '@experience/shared/sequelize/auth-service';
import { INestApplication, Logger, Module } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import {
  CONNECTION_NAME as POD_ADMIN_CONNECTION_NAME,
  Users as PodAdminUsers,
  PodadminSequelizeModule,
} from '@experience/shared/sequelize/podadmin';
import { ParseArgsConfig, parseArgs } from 'util';
import { Sequelize, Transaction } from 'sequelize';
import { getAuth } from '@experience/shared/firebase/admin';
import { getConnectionToken } from '@nestjs/sequelize';

const COMMAND_LINE_OPTIONS: ParseArgsConfig = {
  options: {
    help: {
      type: 'boolean',
      short: 'h',
    },
    email: {
      type: 'string',
      short: 'e',
    },
    'new-email': {
      type: 'string',
      short: 'n',
    },
    update: {
      type: 'boolean',
      short: 'u',
      default: false,
    },
  },
};

@Module({
  imports: [AuthServiceSequelizeModule, PodadminSequelizeModule],
  providers: [
    {
      provide: 'AUTH_SERVICE_USERS_REPOSITORY',
      useValue: AuthServiceUsers,
    },
    {
      provide: 'PODADMIN_USERS_REPOSITORY',
      useValue: PodAdminUsers,
    },
  ],
})
class AppModule {}

const logger = new Logger();

const displayUsage = () => {
  // eslint-disable-next-line no-console
  console.log(`
    update-user-email is a script which allows a users email address to be updated across the
    auth database, podadmin and GIP.

    usage: update-user-email [options]

    options:
      --help, -h : display this help message
      --email, -e : email to be updated
      --new-email, -n : new value of the email
      --update, -u : perform the update, by default this script only logs changes that would be made
  `);

  process.exit(0);
};

const run = async () => {
  let args = null;

  try {
    args = parseArgs(COMMAND_LINE_OPTIONS);
  } catch {
    displayUsage();
  }

  if (args.values.help) {
    displayUsage();
  }

  const { email, update } = args.values;
  const newEmail = args.values['new-email'];

  logger.log({ update }, 'Running with update mode');

  if (!email) {
    logger.error('A value must be provided for email');

    displayUsage();
  }

  if (!newEmail) {
    logger.error('A value must be provided for new email');

    displayUsage();
  }

  const app = await NestFactory.create(AppModule);

  const podAdminConnection = app.get<Sequelize>(
    getConnectionToken(POD_ADMIN_CONNECTION_NAME),
  );

  const authServiceConnection = app.get<Sequelize>(
    getConnectionToken(AUTH_SERVICE_CONNECTION_NAME),
  );

  if (update) {
    const podAdminTransaction: Transaction =
      await podAdminConnection.transaction();

    const authServiceTransaction: Transaction =
      await authServiceConnection.transaction();

    try {
      await updateFirebase(email, newEmail);
      await updatePodAdminDatabase(app, podAdminTransaction, email, newEmail);
      await updateAuthDatabase(app, authServiceTransaction, email, newEmail);

      await podAdminTransaction.commit();
      await authServiceTransaction.commit();
    } catch (error) {
      logger.error({ error }, 'Failed to update user, rolling back changes');

      await updateFirebase(newEmail, email);
      await podAdminTransaction.rollback();
      await authServiceTransaction.rollback();
    }
  } else {
    await checkFirebase(email, newEmail);
    await checkPodAdminDatabase(app, email, newEmail);
    await checkAuthDatabase(app, email, newEmail);
  }

  await app.close();
};

const checkFirebase = async (email, newEmail) => {
  logger.log({ email, newEmail }, 'Checking Firebase');

  const auth = getAuth();
  const userRecord = await auth.getUserByEmail(email);

  logger.log(
    { uid: userRecord.uid, email, newEmail },
    'Firebase record would be updated',
  );
};

const updateFirebase = async (email, newEmail) => {
  logger.log({ email, newEmail }, 'Updating Firebase');

  const auth = getAuth();
  const userRecord = await auth.getUserByEmail(email);

  await auth.updateUser(userRecord.uid, {
    email: newEmail,
  });
};

const checkPodAdminDatabase = async (
  app: INestApplication,
  email,
  newEmail,
) => {
  logger.log({ email, newEmail }, 'Checking podadmin database');

  const podAdminUsersRepository = app.get<typeof PodAdminUsers>(
    'PODADMIN_USERS_REPOSITORY',
  );

  const result = await podAdminUsersRepository.findAndCountAll({
    where: {
      email: email,
    },
    paranoid: true,
  });

  logger.log(
    { rows: result.count, email, newEmail },
    'PodAdmin records that would be updated',
  );
};

const updatePodAdminDatabase = async (
  app: INestApplication,
  transaction,
  email,
  newEmail,
) => {
  logger.log({ email, newEmail }, 'Updating podadmin database');

  const podAdminUsersRepository = app.get<typeof PodAdminUsers>(
    'PODADMIN_USERS_REPOSITORY',
  );

  return await podAdminUsersRepository.update(
    {
      email: newEmail,
    },
    {
      where: {
        email: email,
      },
      paranoid: true,
      transaction,
    },
  );
};

const checkAuthDatabase = async (app: INestApplication, email, newEmail) => {
  logger.log({ email, newEmail }, 'Checking auth database');

  const authServiceUsersRepository = app.get<typeof AuthServiceUsers>(
    'AUTH_SERVICE_USERS_REPOSITORY',
  );

  const result = await authServiceUsersRepository.findAndCountAll({
    where: {
      email: email,
    },
    paranoid: true,
  });

  logger.log(
    { rows: result.count, email, newEmail },
    'Auth database records that would be updated',
  );
};

const updateAuthDatabase = async (
  app: INestApplication,
  transaction,
  email,
  newEmail,
) => {
  logger.log({ email, newEmail }, 'Updating auth database');

  const authServiceUsersRepository = app.get<typeof AuthServiceUsers>(
    'AUTH_SERVICE_USERS_REPOSITORY',
  );

  return await authServiceUsersRepository.update(
    {
      email: newEmail,
    },
    {
      where: {
        email: email,
      },
      paranoid: true,
      transaction,
    },
  );
};

run().catch((error) => logger.error(error));
