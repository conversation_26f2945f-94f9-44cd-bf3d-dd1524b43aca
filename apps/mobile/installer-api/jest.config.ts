 
export default {
  displayName: 'installer-api',
  preset: '../../../jest.preset.js',
  globals: {},
  testEnvironment: 'node',
  transform: {
    '^.+\\.[tj]s$': [
      'ts-jest',
      {
        tsconfig: '<rootDir>/tsconfig.spec.json',
      },
    ],
  },
  moduleFileExtensions: ['ts', 'js', 'html'],
  coverageDirectory: '../../../../coverage/apps/mobile/installer-api',
  setupFiles: ['<rootDir>/jest.env.ts'],
  testPathIgnorePatterns: ['index.spec.ts', '.module.spec.ts'],
};
