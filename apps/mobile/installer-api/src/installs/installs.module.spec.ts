import {
  AppointmentBuilder,
  OutOfServiceReasonKey,
} from '@experience/installer/types';
import { Install, SocketType } from '../database/entities/install.entity';
import { InstallSessionBuilder } from './__fixtures__/installs.builder';
import { Logger } from '@nestjs/common';
import { PublishCommand } from '@aws-sdk/client-sns';
import { S3Service } from '@experience/shared/nest/aws/s3-module';
import { SendEmailCommand } from '@aws-sdk/client-sesv2';
import { createReadStream } from 'fs';
import { faker } from '@faker-js/faker';
import axios from 'axios';
import path from 'path';

const sendMock = jest.fn().mockResolvedValue({});
const publishMessageOnTopicMock = jest.fn().mockResolvedValue({});

jest.mock('@aws-sdk/s3-request-presigner', () => ({
  getSignedUrl: jest.fn().mockResolvedValue('https://example.com'),
}));

jest.mock('@aws-sdk/client-sesv2', () => ({
  ...jest.requireActual('@aws-sdk/client-sesv2'),
  SESv2Client: jest.fn().mockImplementation(() => ({
    send: sendMock,
  })),
  GetSuppressedDestinationCommand: jest.fn().mockImplementation(() => ({})),
}));

jest.mock('@aws-sdk/client-sns', () => ({
  ...jest.requireActual('@aws-sdk/client-sns'),
  SNSClient: jest.fn().mockImplementation(() => ({
    send: publishMessageOnTopicMock,
  })),
}));

const USER_ID = 'wJKDBno8wgPV8XQacRKkUcVu1im3';

export const generatePpid = (prefix = 'PSL-') => {
  const PREFIX_LENGTH_LIMIT = 8;
  const PPID_LENGTH = 15;
  if (prefix.length > PREFIX_LENGTH_LIMIT) {
    throw new Error('Prefix must be fewer than 9 characters');
  }
  return faker.helpers
    .replaceSymbols(`${prefix}###############`)
    .slice(0, PPID_LENGTH);
};

export const describeInstallsModule = (baseUrl: string) => {
  describe('installs module', () => {
    beforeEach(async () => {
      sendMock.mockClear();
      // Create a user profile to be used for creating installations
      try {
        await axios.get(`${baseUrl}/account/profile/${USER_ID}`);
      } catch {
        await axios.post(`${baseUrl}/account/profile`, {
          firstName: 'Test',
          lastName: 'User',
          phoneNumber: '**********',
          companyType: 'sole_trader',
          marketingConsent: true,
          authId: USER_ID,
          email: `user-${USER_ID}@example.com`,
        });
      }
    });

    describe('installs controller', () => {
      it('should return an 401 if user has not completed a profile', async () => {
        await expect(
          axios.post(`${baseUrl}/installs/${crypto.randomUUID()}`, {
            chargerSettings: {
              done: true,
            },
            authId: 'not-a-user',
          }),
        ).rejects.toThrow('Request failed with status code 401');
      });

      it('should return a 400 if an invalid socket value is provided', async () => {
        await expect(
          axios.post(`${baseUrl}/installs/${crypto.randomUUID()}`, {
            chargerSettings: {
              deviceInformation: {
                socket: 'invalid',
              },
              done: true,
            },
            authId: USER_ID,
          }),
        ).rejects.toThrow('Request failed with status code 400');
      });

      it('should return a 400 if an invalid network interface value is provided', async () => {
        await expect(
          axios.post(`${baseUrl}/installs/${crypto.randomUUID()}`, {
            chargerSettings: {
              done: true,
              deviceInformation: {
                pslNumber: generatePpid(),
                socket: 'A',
              },
              connectivity: {
                wifiConnectedState: 'Connected',
                signalStrength: 0,
                networkState: 'disconnected',
                networkInterface: 'asdf',
              },
            },
            authId: USER_ID,
          }),
        ).rejects.toThrow('Request failed with status code 400');
      });

      it('should return a 400 if an invalid network state value is provided', async () => {
        await expect(
          axios.post(`${baseUrl}/installs/${crypto.randomUUID()}`, {
            chargerSettings: {
              done: true,
              deviceInformation: {
                pslNumber: generatePpid(),
                socket: 'A',
              },
              connectivity: {
                wifiConnectedState: 'Connected',
                signalStrength: 0,
                networkState: 'asdf',
                networkInterface: 'ethernet',
              },
            },
            authId: USER_ID,
          }),
        ).rejects.toThrow('Request failed with status code 400');
      });

      it('should return a 400 if other out of service reason added with no description', async () => {
        const ppid = generatePpid();
        const installSession = new InstallSessionBuilder()
          .withChargerSettings(ppid, [{ key: OutOfServiceReasonKey.OTHER }])
          .withAuthId(USER_ID)
          .withCompletedAt(new Date('2023-07-07T09:06:07.000Z'))
          .withAppointment(new AppointmentBuilder().build());

        await expect(
          axios.post(
            `${baseUrl}/installs/${crypto.randomUUID()}`,
            installSession,
          ),
        ).rejects.toThrow('Request failed with status code 400');
      });

      it('should accept a payload to create a new installation', async () => {
        const response = await axios.post(
          `${baseUrl}/installs/${crypto.randomUUID()}`,
          {
            chargerSettings: {
              done: true,
              deviceInformation: {
                pslNumber: generatePpid(),
                socket: 'A',
              },
            },
            authId: USER_ID,
          },
        );
        expect(response.status).toEqual(201);
        expect(response.data).toEqual({
          firstCompletedAt: null,
        });
      });

      it('should accept a payload to update an existing installation', async () => {
        const ppid = generatePpid();
        const uuid = crypto.randomUUID();

        let response = await axios.post(`${baseUrl}/installs/${uuid}`, {
          chargerSettings: {
            done: true,
            deviceInformation: {
              pslNumber: ppid,
            },
          },
          authId: USER_ID,
        });
        expect(response.status).toEqual(201);
        expect(response.data).toEqual({
          firstCompletedAt: null,
        });

        response = await axios.post(`${baseUrl}/installs/${uuid}`, {
          chargerSettings: {
            done: true,
            deviceInformation: {
              pslNumber: ppid,
              socket: 'A',
            },
          },
          authId: USER_ID,
        });
        expect(response.status).toEqual(201);
        expect(response.data).toEqual({
          firstCompletedAt: null,
        });
      });

      it('should log if appointment reference has been reused in a previous domestic order', async () => {
        const appointment = new AppointmentBuilder().build();
        const ppid = generatePpid();
        const ppid2 = generatePpid();
        const completedDate = new Date('2023-07-07T09:06:07.000Z');
        const loggerSpyWarn = jest.spyOn(Logger.prototype, 'warn');

        const installSession = new InstallSessionBuilder()
          .withChargerSettings(ppid)
          .withAuthId(USER_ID)
          .withAppointment(appointment);

        const installSession2 = new InstallSessionBuilder()
          .withChargerSettings(ppid2)
          .withAuthId(USER_ID)
          .withCompletedAt(completedDate)
          .withAppointment(appointment);

        await axios.post(
          `${baseUrl}/installs/${crypto.randomUUID()}`,
          installSession,
        );

        const response = await axios.post(
          `${baseUrl}/installs/${crypto.randomUUID()}`,
          installSession2,
        );

        expect(response.status).toEqual(201);
        expect(response.data).toEqual({
          firstCompletedAt: completedDate.toISOString(),
        });

        expect(loggerSpyWarn).toHaveBeenCalled();
        expect(loggerSpyWarn).toHaveBeenCalledWith(
          {
            appointmentReference: installSession.appointment.reference,
            pslNumber: ppid2,
            duplicatePslNumber: ppid,
          },
          'An installation with the same appointment reference already exists',
        );
      });

      it('should not log if appointment reference has been reused in a previous commercial order', async () => {
        const appointment = new AppointmentBuilder()
          .withUseCase('commercial')
          .build();
        const ppid = generatePpid();
        const ppid2 = generatePpid();
        const completedDate = new Date('2023-07-07T09:06:07.000Z');
        const loggerSpyWarn = jest.spyOn(Logger.prototype, 'warn');

        const installSession = new InstallSessionBuilder()
          .withChargerSettings(ppid)
          .withAuthId(USER_ID)
          .withAppointment(appointment);

        const installSession2 = new InstallSessionBuilder()
          .withChargerSettings(ppid2)
          .withAuthId(USER_ID)
          .withCompletedAt(completedDate)
          .withAppointment(appointment);

        await axios.post(
          `${baseUrl}/installs/${crypto.randomUUID()}`,
          installSession,
        );

        const response = await axios.post(
          `${baseUrl}/installs/${crypto.randomUUID()}`,
          installSession2,
        );

        expect(response.status).toEqual(201);
        expect(response.data).toEqual({
          firstCompletedAt: completedDate.toISOString(),
        });

        expect(loggerSpyWarn).not.toHaveBeenCalled();
        expect(loggerSpyWarn).not.toHaveBeenCalledWith(
          {
            appointmentReference: installSession.appointment.reference,
            pslNumber: ppid2,
            duplicatePslNumber: ppid,
          },
          'An installation with the same appointment reference already exists',
        );
      });

      it('should not log if appointment reference has been reused for the same psl in a domestic order', async () => {
        const appointment = new AppointmentBuilder()
          .withReference('should.not.log.for.same.psl')
          .build();
        const ppid = generatePpid();
        const completedDate = new Date('2023-07-07T09:06:07.000Z');
        const loggerSpyWarn = jest.spyOn(Logger.prototype, 'warn');

        const installSession = new InstallSessionBuilder()
          .withChargerSettings(ppid)
          .withAuthId(USER_ID)
          .withAppointment(appointment);

        const installSession2 = new InstallSessionBuilder()
          .withChargerSettings(ppid)
          .withAuthId(USER_ID)
          .withCompletedAt(completedDate)
          .withAppointment(appointment);

        await axios.post(
          `${baseUrl}/installs/${crypto.randomUUID()}`,
          installSession,
        );

        const response = await axios.post(
          `${baseUrl}/installs/${crypto.randomUUID()}`,
          installSession2,
        );

        expect(response.status).toEqual(201);
        expect(response.data).toEqual({
          firstCompletedAt: completedDate.toISOString(),
        });

        expect(loggerSpyWarn).not.toHaveBeenCalled();
        expect(loggerSpyWarn).not.toHaveBeenCalledWith(
          {
            appointmentReference: installSession.appointment.reference,
            pslNumber: ppid,
            duplicatePslNumber: ppid,
          },
          'An installation with the same appointment reference already exists',
        );
      });

      it('should return the firstCompletedAt on the install with same PSL number', async () => {
        const pslNumber = generatePpid();
        let response = await axios.post(
          `${baseUrl}/installs/${crypto.randomUUID()}`,
          {
            chargerSettings: {
              deviceInformation: {
                pslNumber,
              },
              done: true,
            },
            completedAt: '2023-07-07T09:06:07Z',
            authId: USER_ID,
          },
        );
        expect(response.status).toEqual(201);
        expect(response.data).toEqual({
          firstCompletedAt: '2023-07-07T09:06:07.000Z',
        });

        response = await axios.post(
          `${baseUrl}/installs/${crypto.randomUUID()}`,
          {
            chargerSettings: {
              deviceInformation: {
                pslNumber,
              },
              done: true,
            },
            completedAt: '2023-11-01T09:06:07Z',
            authId: USER_ID,
          },
        );

        expect(response.status).toEqual(201);
        expect(response.data).toEqual({
          firstCompletedAt: '2023-07-07T09:06:07.000Z',
        });
      });

      it('should send an email if there are no previous installs', async () => {
        const pslNumber = generatePpid();

        const response = await axios.post(
          `${baseUrl}/installs/${crypto.randomUUID()}?shouldSendEmail=true`,
          {
            chargerSettings: {
              deviceInformation: {
                pslNumber,
              },
              done: true,
            },
            completedAt: '2023-07-07T09:06:07Z',
            authId: USER_ID,
          },
        );

        expect(response.status).toEqual(201);
        expect(response.data).toEqual({
          firstCompletedAt: '2023-07-07T09:06:07.000Z',
        });

        expect(sendMock).toHaveBeenCalledWith(expect.any(SendEmailCommand));
      });

      it('should not send an email if there is already a completed install', async () => {
        const pslNumber = generatePpid();
        const response = await axios.post(
          `${baseUrl}/installs/${crypto.randomUUID()}?shouldSendEmail=true`,
          {
            chargerSettings: {
              deviceInformation: {
                pslNumber,
              },
              done: true,
            },
            completedAt: '2023-07-07T09:06:07Z',
            authId: USER_ID,
          },
        );

        sendMock.mockClear();

        await axios.post(
          `${baseUrl}/installs/${crypto.randomUUID()}?shouldSendEmail=true`,
          {
            chargerSettings: {
              deviceInformation: {
                pslNumber,
              },
              done: true,
            },
            completedAt: '2023-07-07T09:06:07Z',
            authId: USER_ID,
          },
        );

        expect(response.status).toEqual(201);
        expect(response.data).toEqual({
          firstCompletedAt: '2023-07-07T09:06:07.000Z',
        });
        expect(sendMock).not.toHaveBeenCalled();
      });

      it('should send an email if a previous install does not have a completedAt date', async () => {
        const pslNumber = generatePpid();
        await axios.post(
          `${baseUrl}/installs/${crypto.randomUUID()}?shouldSendEmail=true`,
          {
            chargerSettings: {
              deviceInformation: {
                pslNumber,
              },
              done: true,
            },
            authId: USER_ID,
          },
        );

        sendMock.mockClear();

        const response = await axios.post(
          `${baseUrl}/installs/${crypto.randomUUID()}?shouldSendEmail=true`,
          {
            chargerSettings: {
              deviceInformation: {
                pslNumber,
              },
              done: true,
            },
            completedAt: '2023-07-07T09:06:07Z',
            authId: USER_ID,
          },
        );

        expect(response.status).toEqual(201);
        expect(response.data).toEqual({
          firstCompletedAt: '2023-07-07T09:06:07.000Z',
        });
        expect(sendMock).toHaveBeenCalledWith(expect.any(SendEmailCommand));
      });

      it('should send an install complete event when the feature flag is enabled', async () => {
        const pslNumber = generatePpid();

        const response = await axios.post(
          `${baseUrl}/installs/${crypto.randomUUID()}?shouldPublishInstallationCompletedEvent=true`,
          {
            chargerSettings: {
              deviceInformation: {
                pslNumber,
              },
              done: true,
            },
            appointment: {
              reference: '123456789',
              order: {
                id: 'f3ebac70-af93-4a98-93ba-465c39786604',
                type: 'subscription',
              },
              address: {
                line1: '1 Townfield St',
                line2: '',
                city: 'Chelmsford',
                country: 'Essex',
                postcode: 'CM1 1QJ',
              },
              location: {
                latitude: 51.737462,
                longitude: 0.467493,
                mpan: '1667890123222',
              },
              useCase: 'domestic',
            },
            completedAt: '2023-07-07T09:06:07Z',
            authId: USER_ID,
          },
        );

        expect(response.status).toEqual(201);
        expect(response.data).toEqual({
          firstCompletedAt: '2023-07-07T09:06:07.000Z',
        });

        expect(publishMessageOnTopicMock).toHaveBeenCalled();
        expect(publishMessageOnTopicMock).toHaveBeenCalledWith(
          expect.any(PublishCommand),
        );
      });

      it('should send an install complete event when the feature flag is enabled and there is no appointment data', async () => {
        const pslNumber = generatePpid();

        const response = await axios.post(
          `${baseUrl}/installs/${crypto.randomUUID()}?shouldPublishInstallationCompletedEvent=true`,
          {
            chargerSettings: {
              deviceInformation: {
                pslNumber,
              },
              done: true,
            },
            completedAt: '2023-07-07T09:06:07Z',
            authId: USER_ID,
          },
        );

        expect(response.status).toEqual(201);
        expect(response.data).toEqual({
          firstCompletedAt: '2023-07-07T09:06:07.000Z',
        });

        expect(publishMessageOnTopicMock).toHaveBeenCalled();
        expect(publishMessageOnTopicMock).toHaveBeenCalledWith(
          expect.any(PublishCommand),
        );
      });

      it('should return an empty array if no PPID is supplied', async () => {
        const { data } = await axios.get(
          `${baseUrl}/installs?ppid=this-ppid-does-not-exist`,
        );

        expect(data).toBeInstanceOf(Array);
        expect(data).toHaveLength(0);
      });

      it('should return an array of installations with their images for a given PPID', async () => {
        const PPID = generatePpid();
        const installationGuids = [
          '2991a238-dbac-4ea8-a9cd-1eb2444294c6',
          '4e3fd708-4018-40a3-a8b5-ce59e1ceea3e',
        ];

        // Create installations
        await Promise.all(
          installationGuids.map((guid) =>
            axios.post(`${baseUrl}/installs/${guid}`, {
              chargerSettings: {
                deviceInformation: {
                  pslNumber: PPID,
                },
                done: true,
              },
              completedAt: '2024-04-25T15:52:00Z',
              authId: USER_ID,
            }),
          ),
        );

        const installImageFile = createReadStream(
          path.resolve(__dirname, '../install-images/__fixtures__/cc.jpeg'),
        );

        // Do not actually upload to S3
        jest.spyOn(S3Service.prototype, 'uploadFile').mockResolvedValue(true);
        jest.spyOn(S3Service.prototype, 'deleteFile').mockResolvedValue(true);

        // Assign images to installations
        await Promise.all(
          installationGuids.map((guid) =>
            axios.put(
              `${baseUrl}/install-images`,
              {
                guid,
                label: 'front',
                file: installImageFile,
              },
              {
                headers: {
                  'Content-Type': 'multipart/form-data',
                },
              },
            ),
          ),
        );

        const { data } = await axios.get(`${baseUrl}/installs?ppid=${PPID}`);

        expect(data).toBeInstanceOf(Array);
        expect(data).toHaveLength(2);

        data.forEach((installation: Install) => {
          expect(installationGuids).toContain(installation.guid);
          expect(installation.images).toBeInstanceOf(Array);
          expect(installation.images).toHaveLength(1);
          expect(installation.user).toEqual(
            expect.objectContaining({
              authId: expect.any(String),
              email: expect.stringContaining('@'),
            }),
          );
          expect(
            installation.chargerSettings.deviceInformation.pslNumber,
          ).toEqual(PPID);
        });
      });

      it('should successfully create and retrieve a full install payload', async () => {
        const ppid = generatePpid();
        const installSession = new InstallSessionBuilder()
          .withChargerSettings(ppid)
          .withAuthId(USER_ID)
          .withCompletedAt(new Date('2023-07-07T09:06:07.000Z'))
          .withAppointment(new AppointmentBuilder().build());

        const { status: postStatus } = await axios.post(
          `${baseUrl}/installs/${crypto.randomUUID()}`,
          installSession,
        );

        expect(postStatus).toEqual(201);

        const { data, status: getStatus } = await axios.get(
          `${baseUrl}/installs?ppid=${ppid}`,
        );

        expect(getStatus).toEqual(200);

        expect(data).toEqual([
          {
            guid: expect.any(String),
            id: expect.any(Number),
            pslNumber: ppid,
            socket: SocketType.A,
            chargerSettings: installSession.chargerSettings,
            appointment: {
              reference: installSession.appointment.reference,
            },
            user: expect.objectContaining({
              authId: USER_ID,
              email: expect.stringContaining('@'),
            }),
            images: [],
            createdAt: expect.any(String),
            updatedAt: expect.any(String),
            completedAt: '2023-07-07T09:06:07.000Z',
          },
        ]);
      });

      it('should successfully create and retrieve a full install payload with out of service reasons', async () => {
        const ppid = generatePpid();
        const installSession = new InstallSessionBuilder()
          .withChargerSettings(ppid, [
            { key: OutOfServiceReasonKey.OTHER, description: 'help' },
            { key: OutOfServiceReasonKey.LOOPED_SUPPLY },
          ])
          .withAuthId(USER_ID)
          .withCompletedAt(new Date('2023-07-07T09:06:07.000Z'))
          .withAppointment(new AppointmentBuilder().build());

        const { status: postStatus } = await axios.post(
          `${baseUrl}/installs/${crypto.randomUUID()}`,
          installSession,
        );

        expect(postStatus).toEqual(201);

        const { data, status: getStatus } = await axios.get(
          `${baseUrl}/installs?ppid=${ppid}`,
        );

        expect(getStatus).toEqual(200);

        expect(data).toEqual([
          {
            guid: expect.any(String),
            id: expect.any(Number),
            pslNumber: ppid,
            socket: SocketType.A,
            chargerSettings: installSession.chargerSettings,
            appointment: {
              reference: installSession.appointment.reference,
            },
            user: expect.objectContaining({
              authId: USER_ID,
              email: expect.stringContaining('@'),
            }),
            images: [],
            createdAt: expect.any(String),
            updatedAt: expect.any(String),
            completedAt: '2023-07-07T09:06:07.000Z',
          },
        ]);
      });
    });
  });
};
