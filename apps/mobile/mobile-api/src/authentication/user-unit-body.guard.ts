import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common';
import { UsersService } from '../users/users.service';

@Injectable()
export class UserUnitBodyGuard implements CanActivate {
  constructor(private readonly usersService: UsersService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();

    const { user, body } = request;

    try {
      return await this.usersService.doesUserOwnCharger(user.uuid, body.ppid);
    } catch {
      return false;
    }
  }
}
