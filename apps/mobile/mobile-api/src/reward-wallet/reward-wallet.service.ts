import {
  AccountTransactionDTO,
  AccountTransactionRewardsAccrualMetadata,
  AccountTransactionRewardsPayoutMetadata,
  AccountTransactionRewardsRefundMetadata,
  RewardWalletsApi,
  RewardsApi,
} from '@experience/mobile/rewards-api/axios';
import {
  BankAccountsApi,
  TransactionDTOStatusEnum,
  TransactionsApi,
} from '@experience/mobile/payments-api/axios';
import {
  CanNotPayoutToAnotherUsersBankAccountError,
  CanNotPayoutToNonExistentBankAccountError,
  FailedToPayoutRewardWalletError,
  FailedToRetrieveRewardWalletTransactionsError,
  FailedToRetrieveWalletBalanceSummaryError,
  WalletBalanceNotMetPayoutThresholdError,
  WalletBalanceSummaryNotFoundError,
} from './reward-wallet.errors';
import { HttpStatusCode, isAxiosError } from 'axios';
import { Injectable, Logger } from '@nestjs/common';
import {
  PayoutTransactionTypeStatus,
  RewardWalletDTO,
  RewardWalletTransactionDTO,
  RewardWalletTransactionsDTO,
} from './reward-wallet.types';
import { REWARD_WALLET_PAYMENT_THRESHOLD_GBP } from './reward-wallet.constants';

@Injectable()
export class RewardWalletService {
  private readonly logger = new Logger(RewardWalletService.name);

  constructor(
    private readonly rewardsWalletApi: RewardWalletsApi,
    private readonly rewardsApi: RewardsApi,
    private readonly bankAccountsApi: BankAccountsApi,
    private readonly transactionsApi: TransactionsApi,
  ) {}

  private async getTotalWithdrawn(authId: string): Promise<number> {
    const { data: bankAccounts } =
      await this.bankAccountsApi.bankAccountControllerSearchBankAccounts(
        authId,
      );

    const totals = await Promise.all(
      bankAccounts.map(async (account) => {
        const { data } =
          await this.bankAccountsApi.bankAccountControllerGetBankAccountTransactionSummary(
            account.id,
            'REWARDS',
          );

        return (
          data.totalWithdrawn.find((i) => i.currency === 'GBP')?.amount ?? 0
        );
      }),
    );

    return totals.reduce((i, total) => total + i, 0);
  }

  async getWalletBalanceSummary(authId: string): Promise<RewardWalletDTO> {
    this.logger.log({ authId }, 'getting wallet balance summary');

    try {
      const { data } =
        await this.rewardsWalletApi.walletControllerGetWalletBalanceSummary(
          authId,
        );

      return {
        ...data,
        payments: {
          thresholdGbp: REWARD_WALLET_PAYMENT_THRESHOLD_GBP,
          totalWithdrawnGbp: await this.getTotalWithdrawn(authId),
        },
      };
    } catch (error) {
      this.logger.error(
        { authId, error },
        'failed to get wallet balance summary',
      );

      if (isAxiosError(error)) {
        switch (error.status) {
          case HttpStatusCode.NotFound:
            throw new WalletBalanceSummaryNotFoundError();
        }
      }

      throw new FailedToRetrieveWalletBalanceSummaryError();
    }
  }

  async payoutWalletBalance(
    authId: string,
    bankAccountId: string,
  ): Promise<void> {
    this.logger.log(
      { authId, bankAccountId },
      'attempting to pay out reward wallet',
    );

    try {
      const { data: balance } =
        await this.rewardsWalletApi.walletControllerGetWalletBalanceSummary(
          authId,
        );

      if (balance.rewards.balanceGbp < REWARD_WALLET_PAYMENT_THRESHOLD_GBP) {
        this.logger.log(
          {
            authId,
            balance: balance.rewards.balanceGbp,
            threshold: REWARD_WALLET_PAYMENT_THRESHOLD_GBP,
          },
          'wallet balance has not met payout threshold',
        );

        throw new WalletBalanceNotMetPayoutThresholdError();
      }

      const { status: bankAccountStatus, data: bankAccount } =
        await this.bankAccountsApi.bankAccountControllerGetBankAccountById(
          bankAccountId,
          { validateStatus: (status) => [200, 404].includes(status) },
        );

      if (bankAccountStatus === 404) {
        throw new CanNotPayoutToNonExistentBankAccountError();
      }

      if (bankAccount.userId !== authId) {
        this.logger.log(
          { authId, bankAccountUserId: bankAccount.userId },
          'user does not own given bank account. not paying out',
        );

        throw new CanNotPayoutToAnotherUsersBankAccountError();
      }

      this.logger.log(
        { authId, bankAccountId },
        'confirmed user owns bank account. creating payout',
      );

      const { data: rewardPayout } =
        await this.rewardsWalletApi.walletControllerHandleAction(authId, {
          action: 'REWARD_PAYOUT',
          bankAccountId,
        });

      this.logger.log(
        { authId, bankAccountId, rewardPayout },
        'created reward payout',
      );

      return;
    } catch (error) {
      if (
        error instanceof WalletBalanceNotMetPayoutThresholdError ||
        error instanceof CanNotPayoutToAnotherUsersBankAccountError ||
        error instanceof CanNotPayoutToNonExistentBankAccountError
      ) {
        throw error;
      }

      this.logger.error({ error }, 'failed to payout reward wallet');

      throw new FailedToPayoutRewardWalletError();
    }
  }

  private isRewardsAccrualTransaction(
    transaction: AccountTransactionDTO,
  ): transaction is AccountTransactionDTO & {
    metadata: AccountTransactionRewardsAccrualMetadata;
  } {
    return transaction.reference === 'REWARDS_ACCRUAL';
  }

  private isRewardsPayoutTransaction(
    transaction: AccountTransactionDTO,
  ): transaction is AccountTransactionDTO & {
    metadata: AccountTransactionRewardsPayoutMetadata;
  } {
    return transaction.reference === 'REWARDS_PAYOUT';
  }

  private isRewardsRefundTransaction(
    transaction: AccountTransactionDTO,
  ): transaction is AccountTransactionDTO & {
    metadata: AccountTransactionRewardsRefundMetadata;
  } {
    return transaction.reference === 'REWARDS_REFUND';
  }

  async getRewardWalletTransactions(
    authId: string,
    count: number,
    lastKey?: string,
  ): Promise<RewardWalletTransactionsDTO> {
    this.logger.log(
      { authId, lastKey, count },
      'retrieving reward wallet transactions',
    );

    try {
      const { data: response } =
        await this.rewardsApi.rewardsControllerGetRewardsTransactions(
          authId,
          lastKey,
          count,
        );

      const transactions: RewardWalletTransactionDTO[] = [];

      for (const tx of response.transactions) {
        if (
          !['REWARDS_ACCRUAL', 'REWARDS_PAYOUT', 'REWARDS_REFUND'].includes(
            tx.reference,
          )
        ) {
          this.logger.error({ tx }, 'unexpected reference type');

          continue;
        }

        if (this.isRewardsAccrualTransaction(tx)) {
          transactions.push(await this.mapTransactionToMilesCharged(tx));
        }

        if (this.isRewardsPayoutTransaction(tx)) {
          const mappedPayout = await this.mapTransactionToPayout(tx);

          if (mappedPayout !== null) {
            transactions.push(mappedPayout);
          }
        }

        if (this.isRewardsRefundTransaction(tx)) {
          const mappedRefund = await this.mapTransactionToPayoutRefunded(tx);

          if (mappedRefund !== null) {
            transactions.push(mappedRefund);
          }
        }
      }

      return {
        transactions,
        meta: {
          lastKey: response.meta.lastKey,
        },
      };
    } catch (error) {
      this.logger.error(
        { error },
        'failed to retrieve reward wallet transactions',
      );

      throw new FailedToRetrieveRewardWalletTransactionsError();
    }
  }

  private async mapTransactionToMilesCharged(
    tx: AccountTransactionDTO & {
      metadata: AccountTransactionRewardsAccrualMetadata;
    },
  ): Promise<RewardWalletTransactionDTO> {
    return {
      type: 'MILES_CHARGED',
      timestamp: tx.date,
      amount: tx.amount,
      chargeId: tx.metadata.chargeId,
    };
  }

  private async mapTransactionToPayout(
    tx: AccountTransactionDTO & {
      metadata: AccountTransactionRewardsPayoutMetadata;
    },
  ): Promise<RewardWalletTransactionDTO | null> {
    const { data: payments } =
      await this.transactionsApi.transactionsControllerSearchTransactions(
        undefined,
        undefined,
        undefined,
        tx.metadata.rewardsTransactionId,
      );

    if (payments.length !== 1) {
      this.logger.error(
        { tx, payments },
        'unexpected amount of payments found for transaction id',
      );

      return null;
    }

    const [ptx] = payments;

    return {
      type: 'PAYOUT',
      timestamp: tx.date,
      amount: {
        miles: Math.abs(tx.amount),
        gbp: ptx.amount,
      },
      bankAccountId: ptx.bankAccountId,
      transactionId: ptx.id,
      status: this.mapPaymentStatus(ptx.status),
    };
  }

  private mapPaymentStatus(
    status: TransactionDTOStatusEnum,
  ): PayoutTransactionTypeStatus {
    switch (status) {
      case 'SUCCESS':
        return PayoutTransactionTypeStatus.SENT;

      case 'DECLINED':
      case 'ERROR':
        return PayoutTransactionTypeStatus.REFUNDED;

      default:
        return PayoutTransactionTypeStatus.PENDING;
    }
  }

  private async mapTransactionToPayoutRefunded(
    tx: AccountTransactionDTO & {
      metadata: AccountTransactionRewardsRefundMetadata;
    },
  ): Promise<RewardWalletTransactionDTO | null> {
    try {
      const { data: ptx } =
        await this.transactionsApi.transactionsControllerGetTransactionsById(
          tx.metadata.paymentTransactionId,
        );

      return {
        type: 'PAYOUT_REFUNDED',
        timestamp: tx.date,
        amount: {
          miles: Math.abs(tx.amount),
          gbp: ptx.amount,
        },
        bankAccountId: ptx.bankAccountId,
        transactionId: ptx.id,
      };
    } catch {
      this.logger.error(
        { tx },
        'failed to retrieve payout refunded transaction',
      );

      return null;
    }
  }
}
