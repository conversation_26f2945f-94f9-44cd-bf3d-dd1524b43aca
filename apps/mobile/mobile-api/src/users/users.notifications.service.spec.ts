import { AxiosResponse } from 'axios';
import { NotificationsApi } from '@experience/driver-account-api/api-client';
import { Test, TestingModule } from '@nestjs/testing';

import { UsersNotificationsService } from './users.notifications.service';
import { jest } from '@jest/globals';

describe('UsersNotificationsService', () => {
  const AUTH_ID = 'authId';
  const TOKEN = 'token';

  let service: UsersNotificationsService;
  let notificationsApi: NotificationsApi;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UsersNotificationsService,
        { provide: NotificationsApi, useValue: new NotificationsApi() },
      ],
    }).compile();

    service = module.get<UsersNotificationsService>(UsersNotificationsService);
    notificationsApi = module.get<NotificationsApi>(NotificationsApi);
  });

  afterEach(() => jest.resetAllMocks());

  describe('getTokens()', () => {
    it('should return all tokens for a user', async () => {
      const tokens = [
        [
          {
            token:
              'd7g1h2j3k4l5m6n7o8p9q0r1s2t3u4v5w6x7y8z9a0b1c2d3e4f5g6h7i8j9k0l',
            timestamp: '2024-10-24T13:53:00.000Z',
          },
        ],
      ];

      jest
        .spyOn(notificationsApi, 'notificationsControllerGetTokens')
        .mockResolvedValueOnce({
          data: tokens,
          status: 200,
        } as AxiosResponse);

      const users = await service.getTokens(AUTH_ID);

      expect(users).toEqual(tokens);

      expect(
        notificationsApi.notificationsControllerGetTokens,
      ).toHaveBeenCalledTimes(1);
      expect(
        notificationsApi.notificationsControllerGetTokens,
      ).toHaveBeenCalledWith(AUTH_ID);
    });

    it('should throw exception if driver account get request throws exception', async () => {
      const error = new Error('Oh no');

      jest
        .spyOn(notificationsApi, 'notificationsControllerGetTokens')
        .mockRejectedValueOnce(error);

      await expect(service.getTokens('authId')).rejects.toThrow(error);

      expect(
        notificationsApi.notificationsControllerGetTokens,
      ).toHaveBeenCalledTimes(1);
      expect(
        notificationsApi.notificationsControllerGetTokens,
      ).toHaveBeenCalledWith(AUTH_ID);
    });
  });

  describe('deleteToken()', () => {
    it('should delete token for a user', async () => {
      jest
        .spyOn(notificationsApi, 'notificationsControllerDeleteToken')
        .mockResolvedValue({
          status: 204,
        } as AxiosResponse);

      await service.deleteToken(AUTH_ID, TOKEN);

      expect(
        notificationsApi.notificationsControllerDeleteToken,
      ).toHaveBeenCalledTimes(1);
      expect(
        notificationsApi.notificationsControllerDeleteToken,
      ).toHaveBeenCalledWith(AUTH_ID, TOKEN);
    });

    it('should throw exception if driver account delete request throws exception', async () => {
      const error = new Error('Oh no');

      jest
        .spyOn(notificationsApi, 'notificationsControllerDeleteToken')
        .mockRejectedValueOnce(error);

      await expect(service.deleteToken(AUTH_ID, TOKEN)).rejects.toThrow(error);

      expect(
        notificationsApi.notificationsControllerDeleteToken,
      ).toHaveBeenCalledTimes(1);
      expect(
        notificationsApi.notificationsControllerDeleteToken,
      ).toHaveBeenCalledWith(AUTH_ID, TOKEN);
    });
  });

  describe('saveTokens()', () => {
    it('should save token for user', async () => {
      const token = {
        token: TOKEN,
        timestamp: '2024-10-24T13:53:00.000Z',
      };

      jest
        .spyOn(notificationsApi, 'notificationsControllerSaveToken')
        .mockResolvedValue({
          status: 201,
        } as AxiosResponse);

      await service.saveToken(AUTH_ID, token);

      expect(
        notificationsApi.notificationsControllerSaveToken,
      ).toHaveBeenCalledTimes(1);
      expect(
        notificationsApi.notificationsControllerSaveToken,
      ).toHaveBeenCalledWith(AUTH_ID, token);
    });

    it('should throw exception if driver account post requests throws exception', async () => {
      const error = new Error('Oh no');

      const token = {
        token: TOKEN,
        timestamp: '2024-10-24T13:53:00.000Z',
      };

      jest
        .spyOn(notificationsApi, 'notificationsControllerSaveToken')
        .mockRejectedValueOnce(error);

      await expect(service.saveToken(AUTH_ID, token)).rejects.toThrow(error);

      expect(
        notificationsApi.notificationsControllerSaveToken,
      ).toHaveBeenCalledTimes(1);
      expect(
        notificationsApi.notificationsControllerSaveToken,
      ).toHaveBeenCalledWith(AUTH_ID, token);
    });
  });
});
