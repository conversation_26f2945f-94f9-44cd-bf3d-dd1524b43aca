import { Api3TokenService } from './api3.token.service';
import {
  AuthResponse,
  AuthService,
} from '@experience/mobile/nest/auth-service';
import { CACHED_API3_TOKEN, NON_EXPIRING_TTL } from './api3.constants';
import { CACHE_MANAGER, CacheModule } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';
import { ConfigModule } from '@nestjs/config';
import { Test, TestingModule } from '@nestjs/testing';
import { decodeJwt } from 'jose';
 
import { generateToken } from '@experience/mobile/test/mocking';

describe('Api3TokenService', () => {
  let authService: AuthService;
  let api3TokenService: Api3TokenService;
  let cache: Cache;

  const API3_CLIENT_TOKEN = generateToken('DRIVER_AUTH_PRIVATE_KEY', '30d');
  const API3_CLIENT_TOKEN_ALMOST_EXPIRED = generateToken(
    'DRIVER_AUTH_PRIVATE_KEY',
    '1d'
  );

  const authResponse: AuthResponse = {
    token_type: 'Bearer',
    access_token: API3_CLIENT_TOKEN,
    expires_in: 90,
  };

  const createModule = async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [ConfigModule, CacheModule.register()],
      providers: [AuthService, Api3TokenService],
    }).compile();

    authService = module.get<AuthService>(AuthService);
    cache = module.get<Cache>(CACHE_MANAGER);
    jest
      .spyOn(AuthService.prototype, 'getToken')
      .mockResolvedValueOnce(authResponse);
    api3TokenService = module.get<Api3TokenService>(Api3TokenService);
  };
  beforeEach(async () => {
    jest.clearAllMocks();
    await createModule();
  });

  it('should be defined', () => {
    expect(authService).toBeDefined();
    expect(api3TokenService).toBeDefined();
  });

  it('should not refresh a token that is still valid for more than a day', async () => {
    await cache.set(CACHED_API3_TOKEN, API3_CLIENT_TOKEN, NON_EXPIRING_TTL);
    await api3TokenService.refreshApi3Token('id');
    expect(AuthService.prototype.getToken).toHaveBeenCalledTimes(0);
  });

  const assertToken = (
    api3Token: string,
    expectedApi3Token: string,
    id: string
  ) => {
    const decodedApi3Token = decodeJwt(api3Token);
    const decodedExpectedToken = decodeJwt(expectedApi3Token);
    expect(decodedApi3Token).toEqual(
      expect.objectContaining(decodedExpectedToken)
    );
    expect(decodedApi3Token.sub).toEqual(id);
  };

  it('should refresh a token that is still valid for less than a day', async () => {
    await cache.set(
      CACHED_API3_TOKEN,
      API3_CLIENT_TOKEN_ALMOST_EXPIRED,
      NON_EXPIRING_TTL
    );

    const mockedService = jest.spyOn(authService, 'getToken');
    mockedService.mockImplementation(() => Promise.resolve(authResponse));

    const api3Token = await api3TokenService.refreshApi3Token('id');
    expect(AuthService.prototype.getToken).toHaveBeenCalledTimes(1);
    assertToken(api3Token, API3_CLIENT_TOKEN, 'id');
  });

  it('should create a token when no token exists on startup', async () => {
    await cache.clear();
    const mockedService = jest.spyOn(authService, 'getToken');
    mockedService.mockImplementation(() => Promise.resolve(authResponse));

    const api3Token = await api3TokenService.refreshApi3Token('id');
    expect(AuthService.prototype.getToken).toHaveBeenCalledTimes(1);
    assertToken(api3Token, API3_CLIENT_TOKEN, 'id');
  });
});
