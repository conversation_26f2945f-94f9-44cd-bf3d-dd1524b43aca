import { AuthService } from '@experience/mobile/nest/auth-service';
import { CACHED_API3_TOKEN, NON_EXPIRING_TTL, ONE_DAY } from './api3.constants';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';
import { ConfigService } from '@nestjs/config';
import { Inject, Logger } from '@nestjs/common';
import { decodeJwt } from 'jose';
import jwt from 'jsonwebtoken';

export class Api3TokenService {
  private logger = new Logger('Api3TokenService');
  constructor(
    readonly config: ConfigService,
    readonly authService: AuthService,
    @Inject(CACHE_MANAGER) readonly cacheManager: Cache,
  ) {}
  public async refreshApi3Token(id: string): Promise<string> {
    const cachedToken = await this.refreshServiceToServiceToken();

    return this.getPersonalSignedToken(cachedToken, id);
  }

  public async refreshServiceToServiceToken(): Promise<string> {
    const cachedToken = await this.cacheManager.get<string>(CACHED_API3_TOKEN);

    const initializeToken = !cachedToken;

    if (initializeToken) {
      const token = await this.authService.getToken();
      await this.cacheManager.set(
        CACHED_API3_TOKEN,
        token.access_token,
        NON_EXPIRING_TTL,
      );

      this.logger.log('The service to service token has been created.');
      return token.access_token;
    } else {
      if (this.isTokenExpiring(cachedToken)) {
        const authResponse = await this.authService.getToken();
        await this.cacheManager.set(
          CACHED_API3_TOKEN,
          authResponse.access_token,
          NON_EXPIRING_TTL,
        );
        this.logger.log('The service to service token has been refreshed.');
        return authResponse.access_token;
      }
      return cachedToken;
    }
  }

  private getPersonalSignedToken(token: string, id: string) {
    const decodedToken = JSON.parse(JSON.stringify(decodeJwt(token)));
    const privateKey = this.config
      .get('DRIVER_AUTH_PRIVATE_KEY')
      .replace(/\\n/g, '\n');
    decodedToken.sub = id;

    return jwt.sign(decodedToken, privateKey, {
      algorithm: 'RS256',
    });
  }

  private isTokenExpiring(token: string): boolean {
    const decodedToken = decodeJwt(token);
    const date = new Date(0);

    if (decodedToken.exp) {
      date.setUTCSeconds(decodedToken.exp);
    }

    const diffTime = Math.abs(Date.now() - date.getTime());

    const diffDays = Math.ceil(diffTime / ONE_DAY);
    return diffDays < 2;
  }
}
