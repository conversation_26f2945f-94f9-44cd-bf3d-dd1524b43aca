import { ActionCodeSettings } from 'firebase-admin/lib/auth';
import { ConfigService } from '@nestjs/config';
import { EmailTheme } from '../constants';
import { EmailThemeService } from '../email-theme/email-theme.service';
import { I18nService } from 'nestjs-i18n';
import { Injectable, Logger } from '@nestjs/common';
import { SendRecoverFactorRequest } from '@experience/mobile/driver-account/domain/auth';
import {
  SimpleEmailService,
  getTemplateStrings,
} from '@experience/shared/nest/aws/ses-module';
import { getAuth } from '@experience/shared/firebase/admin';
import { getEmailThemeBaseUrl, getEmailThemeSenderString } from '../auth.utils';
import { getLanguageFromCode } from '@experience/shared/nest/utils';
import { injectParametersIntoTemplateString } from '@experience/shared/typescript/utils';

interface RecoverFactorParameters {
  emailAddress: string;
  actionUrl: string;
  theme: EmailTheme;
  imageUrl: string;
}

@Injectable()
export class RecoverFactorService {
  private readonly logger = new Logger(RecoverFactorService.name);
  constructor(
    private readonly simpleEmailService: SimpleEmailService,
    private readonly config: ConfigService,
    private readonly i18n: I18nService,
    private readonly emailThemeService: EmailThemeService,
  ) {}

  async recoverFactor(
    request: SendRecoverFactorRequest,
    languageCode: string,
    appName?: string,
  ): Promise<void> {
    this.logger.log({ request }, 'Attempting to send recover factor email');
    const { email, recover_factor_continue_url } = request;

    try {
      const enrolled = await this.isUserEnrolledIn2FA(request.email);

      if (!enrolled) {
        this.logger.log({ request }, 'User was not enrolled in 2FA');

        return;
      }

      const theme = await this.emailThemeService.pick({
        appName,
        email: request.email,
      });
      const url = getEmailThemeBaseUrl(theme);
      const recoverFactorLink = await this.generateRecoverFactorLink(
        email,
        languageCode,
        url,
        recover_factor_continue_url,
      );

      const params: RecoverFactorParameters = {
        actionUrl: recoverFactorLink,
        emailAddress: email,
        imageUrl: url,
        theme: theme,
      };

      this.logger.log({ params }, 'Sending recover factor email');

      return this.sendRecoverFactorEmail(params, languageCode);
    } catch (error) {
      this.logger.log({ error }, 'Error sending recover factor email');

      throw error;
    }
  }

  async generateRecoverFactorLink(
    email: string,
    languageCode = 'en',
    url: string,
    recover_factor_continue_url?: string,
  ) {
    const actionSettings: ActionCodeSettings | undefined =
      recover_factor_continue_url
        ? { url: recover_factor_continue_url }
        : undefined;

    return getAuth()
      .generatePasswordResetLink(email, actionSettings)
      .then((passwordResetLink) => new URL(passwordResetLink))
      .then((passwordResetUrl) => {
        const lang = getLanguageFromCode(languageCode);
        let newUrl = `${url}/${lang}/auth/action${passwordResetUrl.search}`;

        newUrl = newUrl
          .replace('lang=en', `lang=${lang}`)
          .replace('resetPassword', 'recoverFactor');

        return newUrl;
      });
  }

  private async sendRecoverFactorEmail(
    params: RecoverFactorParameters,
    languageCode = 'en',
  ) {
    const lang = getLanguageFromCode(languageCode);

    const { htmlTemplateString, plainTextTemplateString } = getTemplateStrings(
      `./assets/mobile-account/email-templates/${lang}/${params.theme}/recover-factor-admin`,
    );

    const subject = this.i18n.t('auth-emails.recover_factor.subject', { lang });

    await this.simpleEmailService.sendEmail({
      bodyHtml: injectParametersIntoTemplateString(htmlTemplateString, {
        ...params,
      }),
      bodyText: injectParametersIntoTemplateString(plainTextTemplateString, {
        ...params,
      }),
      subject,
      to: params.emailAddress,
      attachment: [],
      cc: [],
      bcc: [],
      sender: getEmailThemeSenderString(params.theme),
    });
  }

  private async isUserEnrolledIn2FA(email: string): Promise<boolean> {
    try {
      const user = await getAuth().getUserByEmail(email);

      const factors = user.multiFactor?.enrolledFactors || [];

      return factors.length > 0;
    } catch {
      return false;
    }
  }
}
