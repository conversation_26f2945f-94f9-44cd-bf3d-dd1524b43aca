import { ConfigService } from '@nestjs/config';
import { ConflictException, Injectable, Logger } from '@nestjs/common';
import { EmailTheme, IDENTITY_SERVICE } from '../constants';
import { EmailThemeService } from '../email-theme/email-theme.service';
import { FirebaseAuthError } from 'firebase-admin/lib/utils/error';
import { I18nService } from 'nestjs-i18n';
import { SendVerifyAndChangeEmailRequest } from '@experience/mobile/driver-account/domain/auth';
import {
  SimpleEmailService,
  getTemplateStrings,
} from '@experience/shared/nest/aws/ses-module';
import { getAuth } from '@experience/shared/firebase/admin';
import { getEmailThemeBaseUrl, getEmailThemeSenderString } from '../auth.utils';
import { getLanguageFromCode } from '@experience/shared/nest/utils';
import { injectParametersIntoTemplateString } from '@experience/shared/typescript/utils';

interface VerifyAndChangeEmailEmailParameters {
  actionUrl: string;
  emailAddress: string;
  service: string;
  theme: EmailTheme;
  imageUrl?: string;
}

@Injectable()
export class VerifyAndChangeEmailService {
  private readonly logger = new Logger(VerifyAndChangeEmailService.name);

  constructor(
    private readonly simpleEmailService: SimpleEmailService,
    private readonly config: ConfigService,
    private readonly i18n: I18nService,
    private readonly emailThemeService: EmailThemeService,
  ) {}

  async sendVerifyAndChangeEmail(
    request: SendVerifyAndChangeEmailRequest,
    languageCode: string,
    appName?: string,
  ): Promise<void> {
    this.logger.log({ request }, 'sending verify and change email');

    await this.checkIfEmailAlreadyExists(request.newEmail);
    await getAuth()
      .generateVerifyAndChangeEmailLink(request.email, request.newEmail)
      .then((verifyAndChangeEmailLink) => new URL(verifyAndChangeEmailLink))
      .then(async (verifyAndChangeEmailUrl) => {
        const language = getLanguageFromCode(languageCode);
        const theme = await this.emailThemeService.pick({
          appName,
          email: request.email,
        });
        const url = getEmailThemeBaseUrl(theme);
        const actionUrl =
          `${url}/${language}/auth/action${verifyAndChangeEmailUrl.search}`.replace(
            'lang=en',
            `lang=${language}`,
          );
        const params: VerifyAndChangeEmailEmailParameters = {
          actionUrl: actionUrl,
          emailAddress: request.newEmail,
          service: IDENTITY_SERVICE,
          imageUrl: url,
          theme: theme,
        };
        this.logger.log({ params }, 'sending verify and change email link');
        await this.sendVerifyAndChangeEmailEmail(params, language);
      })
      .catch((error: FirebaseAuthError) => {
        switch (error.code) {
          case 'auth/email-not-found':
          case 'auth/internal-error':
            this.logger.log(
              { request, error },
              'failed to send verify and change',
            );
            return;
          default:
            this.logger.error(
              { request, error },
              'failed to send verify and change',
            );
            throw error;
        }
      });
  }

  private async checkIfEmailAlreadyExists(email: string) {
    let user;
    try {
      user = await getAuth().getUserByEmail(email);
    } catch {
      this.logger.log('User was not found');
    }
    if (user) {
      throw new ConflictException('The email has already been taken.');
    }
  }

  private async sendVerifyAndChangeEmailEmail(
    params: VerifyAndChangeEmailEmailParameters,
    languageCode = 'en',
  ): Promise<void> {
    const lang = getLanguageFromCode(languageCode);
    const { htmlTemplateString, plainTextTemplateString } = getTemplateStrings(
      `./assets/mobile-account/email-templates/${lang}/${params.theme}/verify-and-change-email-admin`,
    );

    const subject = this.i18n.t('auth-emails.change_email.subject', { lang });

    await this.simpleEmailService.sendEmail({
      bodyHtml: injectParametersIntoTemplateString(htmlTemplateString, {
        ...params,
      }),
      bodyText: injectParametersIntoTemplateString(plainTextTemplateString, {
        ...params,
      }),
      subject,
      to: params.emailAddress,
      attachment: [],
      cc: [],
      bcc: [],
      sender: getEmailThemeSenderString(params.theme),
    });
  }
}
