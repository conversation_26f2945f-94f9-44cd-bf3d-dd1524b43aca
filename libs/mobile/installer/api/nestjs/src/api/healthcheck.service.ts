/**
 * Installer API
 * Installer Backend for Frontend API service
 *
 * The version of the OpenAPI document: 1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
/* tslint:disable:no-unused-variable member-ordering */

import { Injectable, Optional } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import type { AxiosRequestConfig, AxiosResponse } from 'axios';
import { Observable, of, switchMap } from 'rxjs';
import { HealthControllerCheck200Response } from '../model/healthControllerCheck200Response';
import { Configuration } from '../configuration';

@Injectable()
export class HealthcheckService {
  protected basePath = 'http://localhost';
  public defaultHeaders: Record<string, string> = {};
  public configuration = new Configuration();
  protected httpClient: HttpService;

  constructor(
    httpClient: HttpService,
    @Optional() configuration: Configuration
  ) {
    this.configuration = configuration || this.configuration;
    this.basePath = configuration?.basePath || this.basePath;
    this.httpClient = configuration?.httpClient || httpClient;
  }

  /**
   * @param consumes string[] mime-types
   * @return true: consumes contains 'multipart/form-data', false: otherwise
   */
  private canConsumeForm(consumes: string[]): boolean {
    const form = 'multipart/form-data';
    return consumes.includes(form);
  }

  /**
   * get installer API health
   *
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   * @param {*} [healthControllerCheckOpts.config] Override http request option.
   */
  public healthControllerCheck(healthControllerCheckOpts?: {
    config?: AxiosRequestConfig;
  }): Observable<AxiosResponse<HealthControllerCheck200Response>>;
  public healthControllerCheck(healthControllerCheckOpts?: {
    config?: AxiosRequestConfig;
  }): Observable<any> {
    const headers = { ...this.defaultHeaders };

    const accessTokenObservable: Observable<any> = of(null);

    // to determine the Accept header
    const httpHeaderAccepts: string[] = ['application/json'];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = [];
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.get<HealthControllerCheck200Response>(
          `${this.basePath}/health`,
          {
            withCredentials: this.configuration.withCredentials,
            ...healthControllerCheckOpts?.config,
            headers: {
              ...headers,
              ...healthControllerCheckOpts?.config?.headers,
            },
          }
        );
      })
    );
  }
}
