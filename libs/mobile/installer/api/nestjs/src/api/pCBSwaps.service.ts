/**
 * Installer API
 * Installer Backend for Frontend API service
 *
 * The version of the OpenAPI document: 1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
/* tslint:disable:no-unused-variable member-ordering */

import { Injectable, Optional } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import type { AxiosRequestConfig, AxiosResponse } from 'axios';
import { Observable, of, switchMap } from 'rxjs';
import { CreatePcbSwapDto } from '../model/createPcbSwapDto';
import { PcbSwapDto } from '../model/pcbSwapDto';
import { UpdatePcbSwapDto } from '../model/updatePcbSwapDto';
import { Configuration } from '../configuration';

@Injectable()
export class PCBSwapsService {
  protected basePath = 'http://localhost';
  public defaultHeaders: Record<string, string> = {};
  public configuration = new Configuration();
  protected httpClient: HttpService;

  constructor(
    httpClient: HttpService,
    @Optional() configuration: Configuration
  ) {
    this.configuration = configuration || this.configuration;
    this.basePath = configuration?.basePath || this.basePath;
    this.httpClient = configuration?.httpClient || httpClient;
  }

  /**
   * @param consumes string[] mime-types
   * @return true: consumes contains 'multipart/form-data', false: otherwise
   */
  private canConsumeForm(consumes: string[]): boolean {
    const form = 'multipart/form-data';
    return consumes.includes(form);
  }

  /**
   *
   *
   * @param emailedAt Filter results to ones which do not have the emailed_at field
   * @param changedAtBefore Filter results to have a changed_at date before the given timestamp
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   * @param {*} [pcbSwapsControllerGetAllPcbSwapsOpts.config] Override http request option.
   */
  public pcbSwapsControllerGetAllPcbSwaps(
    emailedAt?: 'null',
    changedAtBefore?: any,
    pcbSwapsControllerGetAllPcbSwapsOpts?: { config?: AxiosRequestConfig }
  ): Observable<AxiosResponse<PcbSwapDto[]>>;
  public pcbSwapsControllerGetAllPcbSwaps(
    emailedAt?: 'null',
    changedAtBefore?: any,
    pcbSwapsControllerGetAllPcbSwapsOpts?: { config?: AxiosRequestConfig }
  ): Observable<any> {
    const queryParameters = new URLSearchParams();
    if (emailedAt !== undefined && emailedAt !== null) {
      queryParameters.append('emailedAt', <any>emailedAt);
    }
    if (changedAtBefore !== undefined && changedAtBefore !== null) {
      queryParameters.append('changedAtBefore', <any>changedAtBefore);
    }

    const headers = { ...this.defaultHeaders };

    const accessTokenObservable: Observable<any> = of(null);

    // to determine the Accept header
    const httpHeaderAccepts: string[] = ['application/json'];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = [];
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.get<PcbSwapDto[]>(
          `${this.basePath}/v1/pcb-swaps`,
          {
            params: queryParameters,
            withCredentials: this.configuration.withCredentials,
            ...pcbSwapsControllerGetAllPcbSwapsOpts?.config,
            headers: {
              ...headers,
              ...pcbSwapsControllerGetAllPcbSwapsOpts?.config?.headers,
            },
          }
        );
      })
    );
  }
  /**
   * Register a record of a PCB swap happening
   * For a given PCB swap, register a record of it happening
   * @param createPcbSwapDto
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   * @param {*} [pcbSwapsControllerRegisterPcbSwapOpts.config] Override http request option.
   */
  public pcbSwapsControllerRegisterPcbSwap(
    createPcbSwapDto: CreatePcbSwapDto,
    pcbSwapsControllerRegisterPcbSwapOpts?: { config?: AxiosRequestConfig }
  ): Observable<AxiosResponse<PcbSwapDto>>;
  public pcbSwapsControllerRegisterPcbSwap(
    createPcbSwapDto: CreatePcbSwapDto,
    pcbSwapsControllerRegisterPcbSwapOpts?: { config?: AxiosRequestConfig }
  ): Observable<any> {
    if (createPcbSwapDto === null || createPcbSwapDto === undefined) {
      throw new Error(
        'Required parameter createPcbSwapDto was null or undefined when calling pcbSwapsControllerRegisterPcbSwap.'
      );
    }

    const headers = { ...this.defaultHeaders };

    const accessTokenObservable: Observable<any> = of(null);

    // to determine the Accept header
    const httpHeaderAccepts: string[] = ['application/json'];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = ['application/json'];
    const httpContentTypeSelected: string | undefined =
      this.configuration.selectHeaderContentType(consumes);
    if (httpContentTypeSelected != undefined) {
      headers['Content-Type'] = httpContentTypeSelected;
    }
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.post<PcbSwapDto>(
          `${this.basePath}/v1/pcb-swaps`,
          createPcbSwapDto,
          {
            withCredentials: this.configuration.withCredentials,
            ...pcbSwapsControllerRegisterPcbSwapOpts?.config,
            headers: {
              ...headers,
              ...pcbSwapsControllerRegisterPcbSwapOpts?.config?.headers,
            },
          }
        );
      })
    );
  }
  /**
   * Update a record of a PCB swap
   * For a given PCB swap, update the record
   * @param ppid The PPID of the given charger
   * @param serialNumber The serial number of the PCB swap
   * @param updatePcbSwapDto
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   * @param {*} [pcbSwapsControllerUpdatePcbSwapOpts.config] Override http request option.
   */
  public pcbSwapsControllerUpdatePcbSwap(
    ppid: string,
    serialNumber: string,
    updatePcbSwapDto: UpdatePcbSwapDto,
    pcbSwapsControllerUpdatePcbSwapOpts?: { config?: AxiosRequestConfig }
  ): Observable<AxiosResponse<PcbSwapDto>>;
  public pcbSwapsControllerUpdatePcbSwap(
    ppid: string,
    serialNumber: string,
    updatePcbSwapDto: UpdatePcbSwapDto,
    pcbSwapsControllerUpdatePcbSwapOpts?: { config?: AxiosRequestConfig }
  ): Observable<any> {
    if (ppid === null || ppid === undefined) {
      throw new Error(
        'Required parameter ppid was null or undefined when calling pcbSwapsControllerUpdatePcbSwap.'
      );
    }

    if (serialNumber === null || serialNumber === undefined) {
      throw new Error(
        'Required parameter serialNumber was null or undefined when calling pcbSwapsControllerUpdatePcbSwap.'
      );
    }

    if (updatePcbSwapDto === null || updatePcbSwapDto === undefined) {
      throw new Error(
        'Required parameter updatePcbSwapDto was null or undefined when calling pcbSwapsControllerUpdatePcbSwap.'
      );
    }

    const headers = { ...this.defaultHeaders };

    const accessTokenObservable: Observable<any> = of(null);

    // to determine the Accept header
    const httpHeaderAccepts: string[] = ['application/json'];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = ['application/json'];
    const httpContentTypeSelected: string | undefined =
      this.configuration.selectHeaderContentType(consumes);
    if (httpContentTypeSelected != undefined) {
      headers['Content-Type'] = httpContentTypeSelected;
    }
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.patch<PcbSwapDto>(
          `${this.basePath}/v1/pcb-swaps/${encodeURIComponent(
            String(ppid)
          )}/${encodeURIComponent(String(serialNumber))}`,
          updatePcbSwapDto,
          {
            withCredentials: this.configuration.withCredentials,
            ...pcbSwapsControllerUpdatePcbSwapOpts?.config,
            headers: {
              ...headers,
              ...pcbSwapsControllerUpdatePcbSwapOpts?.config?.headers,
            },
          }
        );
      })
    );
  }
}
