/**
 * Installer API
 * Installer Backend for Frontend API service
 *
 * The version of the OpenAPI document: 1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
/* tslint:disable:no-unused-variable member-ordering */

import { Injectable, Optional } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import type { AxiosRequestConfig, AxiosResponse } from 'axios';
import { Observable, of, switchMap } from 'rxjs';
import { CreateUserProfilePayload } from '../model/createUserProfilePayload';
import { UpdateUserProfilePayload } from '../model/updateUserProfilePayload';
import { UserEmailDto } from '../model/userEmailDto';
import { UserProfile } from '../model/userProfile';
import { Configuration } from '../configuration';

@Injectable()
export class AccountService {
  protected basePath = 'http://localhost';
  public defaultHeaders: Record<string, string> = {};
  public configuration = new Configuration();
  protected httpClient: HttpService;

  constructor(
    httpClient: HttpService,
    @Optional() configuration: Configuration
  ) {
    this.configuration = configuration || this.configuration;
    this.basePath = configuration?.basePath || this.basePath;
    this.httpClient = configuration?.httpClient || httpClient;
  }

  /**
   * @param consumes string[] mime-types
   * @return true: consumes contains 'multipart/form-data', false: otherwise
   */
  private canConsumeForm(consumes: string[]): boolean {
    const form = 'multipart/form-data';
    return consumes.includes(form);
  }

  /**
   * create a new user profile
   * Create a new user profile
   * @param createUserProfilePayload
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   * @param {*} [accountControllerCreateProfileOpts.config] Override http request option.
   */
  public accountControllerCreateProfile(
    createUserProfilePayload: CreateUserProfilePayload,
    accountControllerCreateProfileOpts?: { config?: AxiosRequestConfig }
  ): Observable<AxiosResponse<any>>;
  public accountControllerCreateProfile(
    createUserProfilePayload: CreateUserProfilePayload,
    accountControllerCreateProfileOpts?: { config?: AxiosRequestConfig }
  ): Observable<any> {
    if (
      createUserProfilePayload === null ||
      createUserProfilePayload === undefined
    ) {
      throw new Error(
        'Required parameter createUserProfilePayload was null or undefined when calling accountControllerCreateProfile.'
      );
    }

    const headers = { ...this.defaultHeaders };

    const accessTokenObservable: Observable<any> = of(null);

    // to determine the Accept header
    const httpHeaderAccepts: string[] = ['application/json'];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = ['application/json'];
    const httpContentTypeSelected: string | undefined =
      this.configuration.selectHeaderContentType(consumes);
    if (httpContentTypeSelected != undefined) {
      headers['Content-Type'] = httpContentTypeSelected;
    }
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.post<any>(
          `${this.basePath}/v1/account/profile`,
          createUserProfilePayload,
          {
            withCredentials: this.configuration.withCredentials,
            ...accountControllerCreateProfileOpts?.config,
            headers: {
              ...headers,
              ...accountControllerCreateProfileOpts?.config?.headers,
            },
          }
        );
      })
    );
  }
  /**
   * delete a user profile
   * Delete a user profile
   * @param authId Google Identity Platform user id
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   * @param {*} [accountControllerDeleteProfileOpts.config] Override http request option.
   */
  public accountControllerDeleteProfile(
    authId: string,
    accountControllerDeleteProfileOpts?: { config?: AxiosRequestConfig }
  ): Observable<AxiosResponse<any>>;
  public accountControllerDeleteProfile(
    authId: string,
    accountControllerDeleteProfileOpts?: { config?: AxiosRequestConfig }
  ): Observable<any> {
    if (authId === null || authId === undefined) {
      throw new Error(
        'Required parameter authId was null or undefined when calling accountControllerDeleteProfile.'
      );
    }

    const headers = { ...this.defaultHeaders };

    const accessTokenObservable: Observable<any> = of(null);

    // to determine the Accept header
    const httpHeaderAccepts: string[] = ['application/json'];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = [];
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.delete<any>(
          `${this.basePath}/v1/account/profile/${encodeURIComponent(
            String(authId)
          )}`,
          {
            withCredentials: this.configuration.withCredentials,
            ...accountControllerDeleteProfileOpts?.config,
            headers: {
              ...headers,
              ...accountControllerDeleteProfileOpts?.config?.headers,
            },
          }
        );
      })
    );
  }
  /**
   * get user profile by given filters
   * Get a user profile by a given filter. Accepts only email at the moment
   * @param ppid Allows for searching by PPID
   * @param emailLike Allows for pattern matching on the email field
   * @param email
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   * @param {*} [accountControllerGetByFilterOpts.config] Override http request option.
   */
  public accountControllerGetByFilter(
    ppid?: string,
    emailLike?: string,
    email?: string,
    accountControllerGetByFilterOpts?: { config?: AxiosRequestConfig }
  ): Observable<AxiosResponse<UserProfile[]>>;
  public accountControllerGetByFilter(
    ppid?: string,
    emailLike?: string,
    email?: string,
    accountControllerGetByFilterOpts?: { config?: AxiosRequestConfig }
  ): Observable<any> {
    const queryParameters = new URLSearchParams();
    if (ppid !== undefined && ppid !== null) {
      queryParameters.append('ppid', <any>ppid);
    }
    if (emailLike !== undefined && emailLike !== null) {
      queryParameters.append('emailLike', <any>emailLike);
    }
    if (email !== undefined && email !== null) {
      queryParameters.append('email', <any>email);
    }

    const headers = { ...this.defaultHeaders };

    const accessTokenObservable: Observable<any> = of(null);

    // to determine the Accept header
    const httpHeaderAccepts: string[] = ['application/json'];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = [];
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.get<UserProfile[]>(
          `${this.basePath}/v1/account/profile`,
          {
            params: queryParameters,
            withCredentials: this.configuration.withCredentials,
            ...accountControllerGetByFilterOpts?.config,
            headers: {
              ...headers,
              ...accountControllerGetByFilterOpts?.config?.headers,
            },
          }
        );
      })
    );
  }
  /**
   * retrieve a new user profile
   * Retrieve a new user profile
   * @param authId Google Identity Platform user id
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   * @param {*} [accountControllerGetProfileOpts.config] Override http request option.
   */
  public accountControllerGetProfile(
    authId: string,
    accountControllerGetProfileOpts?: { config?: AxiosRequestConfig }
  ): Observable<AxiosResponse<UserProfile>>;
  public accountControllerGetProfile(
    authId: string,
    accountControllerGetProfileOpts?: { config?: AxiosRequestConfig }
  ): Observable<any> {
    if (authId === null || authId === undefined) {
      throw new Error(
        'Required parameter authId was null or undefined when calling accountControllerGetProfile.'
      );
    }

    const headers = { ...this.defaultHeaders };

    const accessTokenObservable: Observable<any> = of(null);

    // to determine the Accept header
    const httpHeaderAccepts: string[] = ['application/json'];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = [];
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.get<UserProfile>(
          `${this.basePath}/v1/account/profile/${encodeURIComponent(
            String(authId)
          )}`,
          {
            withCredentials: this.configuration.withCredentials,
            ...accountControllerGetProfileOpts?.config,
            headers: {
              ...headers,
              ...accountControllerGetProfileOpts?.config?.headers,
            },
          }
        );
      })
    );
  }
  /**
   * Update user\&#39;s email
   * Update a user\&#39;s email with a new email for an existing email
   * @param userEmailDto
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   * @param {*} [accountControllerUpdateEmailOpts.config] Override http request option.
   */
  public accountControllerUpdateEmail(
    userEmailDto: UserEmailDto,
    accountControllerUpdateEmailOpts?: { config?: AxiosRequestConfig }
  ): Observable<AxiosResponse<UserEmailDto>>;
  public accountControllerUpdateEmail(
    userEmailDto: UserEmailDto,
    accountControllerUpdateEmailOpts?: { config?: AxiosRequestConfig }
  ): Observable<any> {
    if (userEmailDto === null || userEmailDto === undefined) {
      throw new Error(
        'Required parameter userEmailDto was null or undefined when calling accountControllerUpdateEmail.'
      );
    }

    const headers = { ...this.defaultHeaders };

    const accessTokenObservable: Observable<any> = of(null);

    // to determine the Accept header
    const httpHeaderAccepts: string[] = ['application/json'];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = ['application/json'];
    const httpContentTypeSelected: string | undefined =
      this.configuration.selectHeaderContentType(consumes);
    if (httpContentTypeSelected != undefined) {
      headers['Content-Type'] = httpContentTypeSelected;
    }
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.put<UserEmailDto>(
          `${this.basePath}/v1/account/update-email`,
          userEmailDto,
          {
            withCredentials: this.configuration.withCredentials,
            ...accountControllerUpdateEmailOpts?.config,
            headers: {
              ...headers,
              ...accountControllerUpdateEmailOpts?.config?.headers,
            },
          }
        );
      })
    );
  }
  /**
   * update a user profile
   * Update a user profile
   * @param authId Google Identity Platform user id
   * @param updateUserProfilePayload
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   * @param {*} [accountControllerUpdateProfileOpts.config] Override http request option.
   */
  public accountControllerUpdateProfile(
    authId: string,
    updateUserProfilePayload: UpdateUserProfilePayload,
    accountControllerUpdateProfileOpts?: { config?: AxiosRequestConfig }
  ): Observable<AxiosResponse<any>>;
  public accountControllerUpdateProfile(
    authId: string,
    updateUserProfilePayload: UpdateUserProfilePayload,
    accountControllerUpdateProfileOpts?: { config?: AxiosRequestConfig }
  ): Observable<any> {
    if (authId === null || authId === undefined) {
      throw new Error(
        'Required parameter authId was null or undefined when calling accountControllerUpdateProfile.'
      );
    }

    if (
      updateUserProfilePayload === null ||
      updateUserProfilePayload === undefined
    ) {
      throw new Error(
        'Required parameter updateUserProfilePayload was null or undefined when calling accountControllerUpdateProfile.'
      );
    }

    const headers = { ...this.defaultHeaders };

    const accessTokenObservable: Observable<any> = of(null);

    // to determine the Accept header
    const httpHeaderAccepts: string[] = ['application/json'];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = ['application/json'];
    const httpContentTypeSelected: string | undefined =
      this.configuration.selectHeaderContentType(consumes);
    if (httpContentTypeSelected != undefined) {
      headers['Content-Type'] = httpContentTypeSelected;
    }
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.put<any>(
          `${this.basePath}/v1/account/profile/${encodeURIComponent(
            String(authId)
          )}`,
          updateUserProfilePayload,
          {
            withCredentials: this.configuration.withCredentials,
            ...accountControllerUpdateProfileOpts?.config,
            headers: {
              ...headers,
              ...accountControllerUpdateProfileOpts?.config?.headers,
            },
          }
        );
      })
    );
  }
}
