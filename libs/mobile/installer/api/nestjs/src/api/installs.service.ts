/**
 * Installer API
 * Installer Backend for Frontend API service
 *
 * The version of the OpenAPI document: 1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
/* tslint:disable:no-unused-variable member-ordering */

import { Injectable, Optional } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import type { AxiosRequestConfig, AxiosResponse } from 'axios';
import { Observable, of, switchMap } from 'rxjs';
import { InstallResponse } from '../model/installResponse';
import { InstallSession } from '../model/installSession';
import { UpsertInstallResponse } from '../model/upsertInstallResponse';
import { Configuration } from '../configuration';

@Injectable()
export class InstallsService {
  protected basePath = 'http://localhost';
  public defaultHeaders: Record<string, string> = {};
  public configuration = new Configuration();
  protected httpClient: HttpService;

  constructor(
    httpClient: HttpService,
    @Optional() configuration: Configuration
  ) {
    this.configuration = configuration || this.configuration;
    this.basePath = configuration?.basePath || this.basePath;
    this.httpClient = configuration?.httpClient || httpClient;
  }

  /**
   * @param consumes string[] mime-types
   * @return true: consumes contains 'multipart/form-data', false: otherwise
   */
  private canConsumeForm(consumes: string[]): boolean {
    const form = 'multipart/form-data';
    return consumes.includes(form);
  }

  /**
   * create or update installation\&#39;s image
   * Create or update installation\&#39;s image
   * @param label Image\\\&#39;s label
   * @param guid Install\\\&#39;s guid
   * @param file
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   * @param {*} [installImagesControllerCreateOrUpdateOpts.config] Override http request option.
   */
  public installImagesControllerCreateOrUpdate(
    label?: string,
    guid?: string,
    file?: Blob,
    installImagesControllerCreateOrUpdateOpts?: { config?: AxiosRequestConfig }
  ): Observable<AxiosResponse<any>>;
  public installImagesControllerCreateOrUpdate(
    label?: string,
    guid?: string,
    file?: Blob,
    installImagesControllerCreateOrUpdateOpts?: { config?: AxiosRequestConfig }
  ): Observable<any> {
    const headers = { ...this.defaultHeaders };

    const accessTokenObservable: Observable<any> = of(null);

    // to determine the Accept header
    const httpHeaderAccepts: string[] = ['application/json'];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = ['multipart/form-data'];

    const canConsumeForm = this.canConsumeForm(consumes);

    let formParams: { append(param: string, value: any): void };
    let useForm = false;
    const convertFormParamsToString = false;

    // use FormData to transmit files using content-type "multipart/form-data"
    // see https://stackoverflow.com/questions/4007969/application-x-www-form-urlencoded-or-multipart-form-data
    useForm = canConsumeForm;
    if (useForm) {
      formParams = new FormData();
    } else {
      // formParams = new HttpParams({encoder: new CustomHttpUrlEncodingCodec()});
    }

    if (label !== undefined) {
      formParams!.append('label', <any>label);
    }

    if (guid !== undefined) {
      formParams!.append('guid', <any>guid);
    }

    if (file !== undefined) {
      formParams!.append('file', <any>file);
    }

    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.put<any>(
          `${this.basePath}/v1/install-images`,
          convertFormParamsToString ? formParams!.toString() : formParams!,
          {
            withCredentials: this.configuration.withCredentials,
            ...installImagesControllerCreateOrUpdateOpts?.config,
            headers: {
              ...headers,
              ...installImagesControllerCreateOrUpdateOpts?.config?.headers,
            },
          }
        );
      })
    );
  }
  /**
   * Get a list of installations based on a given PPID/PSL number.
   *
   * @param ppid The PPID/PSL number of the device.
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   * @param {*} [installsControllerGetInstallOpts.config] Override http request option.
   */
  public installsControllerGetInstall(
    ppid: string,
    installsControllerGetInstallOpts?: { config?: AxiosRequestConfig }
  ): Observable<AxiosResponse<InstallResponse[]>>;
  public installsControllerGetInstall(
    ppid: string,
    installsControllerGetInstallOpts?: { config?: AxiosRequestConfig }
  ): Observable<any> {
    if (ppid === null || ppid === undefined) {
      throw new Error(
        'Required parameter ppid was null or undefined when calling installsControllerGetInstall.'
      );
    }

    const queryParameters = new URLSearchParams();
    if (ppid !== undefined && ppid !== null) {
      queryParameters.append('ppid', <any>ppid);
    }

    const headers = { ...this.defaultHeaders };

    const accessTokenObservable: Observable<any> = of(null);

    // to determine the Accept header
    const httpHeaderAccepts: string[] = ['application/json'];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = [];
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.get<InstallResponse[]>(
          `${this.basePath}/v1/installs`,
          {
            params: queryParameters,
            withCredentials: this.configuration.withCredentials,
            ...installsControllerGetInstallOpts?.config,
            headers: {
              ...headers,
              ...installsControllerGetInstallOpts?.config?.headers,
            },
          }
        );
      })
    );
  }
  /**
   * create or update installation data
   * Create or update installation data
   * @param guid The unique guid that is created on the client and used to reference the installation
   * @param shouldSendEmail Whether or not an email should be sent to the installer on first installation
   * @param shouldPublishInstallationCompletedEvent Whether or not an installation completed event should be sent on first installation
   * @param installSession
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   * @param {*} [installsControllerUpdateInstallOpts.config] Override http request option.
   */
  public installsControllerUpdateInstall(
    guid: string,
    shouldSendEmail: boolean,
    shouldPublishInstallationCompletedEvent: boolean,
    installSession: InstallSession,
    installsControllerUpdateInstallOpts?: { config?: AxiosRequestConfig }
  ): Observable<AxiosResponse<UpsertInstallResponse>>;
  public installsControllerUpdateInstall(
    guid: string,
    shouldSendEmail: boolean,
    shouldPublishInstallationCompletedEvent: boolean,
    installSession: InstallSession,
    installsControllerUpdateInstallOpts?: { config?: AxiosRequestConfig }
  ): Observable<any> {
    if (guid === null || guid === undefined) {
      throw new Error(
        'Required parameter guid was null or undefined when calling installsControllerUpdateInstall.'
      );
    }

    if (shouldSendEmail === null || shouldSendEmail === undefined) {
      throw new Error(
        'Required parameter shouldSendEmail was null or undefined when calling installsControllerUpdateInstall.'
      );
    }

    if (
      shouldPublishInstallationCompletedEvent === null ||
      shouldPublishInstallationCompletedEvent === undefined
    ) {
      throw new Error(
        'Required parameter shouldPublishInstallationCompletedEvent was null or undefined when calling installsControllerUpdateInstall.'
      );
    }

    if (installSession === null || installSession === undefined) {
      throw new Error(
        'Required parameter installSession was null or undefined when calling installsControllerUpdateInstall.'
      );
    }

    const queryParameters = new URLSearchParams();
    if (shouldSendEmail !== undefined && shouldSendEmail !== null) {
      queryParameters.append('shouldSendEmail', <any>shouldSendEmail);
    }
    if (
      shouldPublishInstallationCompletedEvent !== undefined &&
      shouldPublishInstallationCompletedEvent !== null
    ) {
      queryParameters.append(
        'shouldPublishInstallationCompletedEvent',
        <any>shouldPublishInstallationCompletedEvent
      );
    }

    const headers = { ...this.defaultHeaders };

    const accessTokenObservable: Observable<any> = of(null);

    // to determine the Accept header
    const httpHeaderAccepts: string[] = ['application/json'];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = ['application/json'];
    const httpContentTypeSelected: string | undefined =
      this.configuration.selectHeaderContentType(consumes);
    if (httpContentTypeSelected != undefined) {
      headers['Content-Type'] = httpContentTypeSelected;
    }
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.post<UpsertInstallResponse>(
          `${this.basePath}/v1/installs/${encodeURIComponent(String(guid))}`,
          installSession,
          {
            params: queryParameters,
            withCredentials: this.configuration.withCredentials,
            ...installsControllerUpdateInstallOpts?.config,
            headers: {
              ...headers,
              ...installsControllerUpdateInstallOpts?.config?.headers,
            },
          }
        );
      })
    );
  }
}
