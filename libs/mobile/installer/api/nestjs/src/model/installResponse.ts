/**
 * Installer API
 * Installer Backend for Frontend API service
 *
 * The version of the OpenAPI document: 1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { UserProfile } from './userProfile';
import { InstallImageResponse } from './installImageResponse';
import { AppointmentReferencePayload } from './appointmentReferencePayload';
import { ChargerSettingsPayload } from './chargerSettingsPayload';

export interface InstallResponse {
  /**
   * PSL number
   */
  pslNumber: string;
  /**
   * Charger settings JSON object
   */
  chargerSettings: ChargerSettingsPayload;
  /**
   * Appointment payload
   */
  appointment: AppointmentReferencePayload | null;
  /**
   * Completion date in ISO8601
   */
  completedAt: string | null;
  /**
   * Created date in ISO8601
   */
  createdAt: string;
  /**
   * GUID of the install
   */
  guid: string;
  /**
   * The numerical ID of the install
   */
  id: number;
  /**
   * The images associated with the install
   */
  images: InstallImageResponse[];
  /**
   * A or B respectively for left or right side of twin or null for single unit
   */
  socket?: InstallResponse.SocketEnum | null;
  /**
   * Updated date in ISO8601
   */
  updatedAt: string;
  /**
   * The user profile of the installer
   */
  user: UserProfile;
}
export namespace InstallResponse {
  export type SocketEnum = 'A' | 'B';
  export const SocketEnum = {
    A: 'A' as SocketEnum,
    B: 'B' as SocketEnum,
  };
}
