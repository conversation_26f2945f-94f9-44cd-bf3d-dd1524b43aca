/**
 * Installer API
 * Installer Backend for Frontend API service
 *
 * The version of the OpenAPI document: 1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { OutOfServiceReason } from './outOfServiceReason';

export interface DeviceConfig {
  powerGenerationSystemInstalled: boolean;
  breakerSize: number;
  powerBalancingSensorInstalled: boolean;
  powerBalancingEnabled: boolean;
  householdMaxSupply: number;
  powerRatingPerPhase: number;
  outOfService: boolean;
  outOfServiceReasons?: OutOfServiceReason[];
  linkySchedulesEnabled?: boolean;
  powerBalancingSensor?: DeviceConfig.PowerBalancingSensorEnum;
}
export namespace DeviceConfig {
  export type PowerBalancingSensorEnum =
    | 'CT_CLAMP_1'
    | 'LINKY'
    | 'ARRAY'
    | 'NONE';
  export const PowerBalancingSensorEnum = {
    CtClamp1: 'CT_CLAMP_1' as PowerBalancingSensorEnum,
    Linky: 'LINKY' as PowerBalancingSensorEnum,
    Array: 'ARRAY' as PowerBalancingSensorEnum,
    None: 'NONE' as PowerBalancingSensorEnum,
  };
}
