/**
 * Mobile API
 * Mobile Backend for Frontend API service
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { RewardsChargerDto } from './rewardsChargerDto';

export interface RewardsDto {
  /**
   * The total amount of reward miles from individual chargers
   */
  totalMiles: number;
  /**
   * The balance in the lowest unit of currency (pence for GBP)
   */
  balance: number;
  /**
   * The currency represented by the balance
   */
  currency: string;
  /**
   * The minimum amount of rewards miles required for payout
   */
  payoutThreshold: number;
  /**
   * The chargers for which the user is eligible for the reward
   */
  chargers: RewardsChargerDto[];
}
