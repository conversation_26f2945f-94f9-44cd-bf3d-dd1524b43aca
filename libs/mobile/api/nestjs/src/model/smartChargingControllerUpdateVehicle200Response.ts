/**
 * Mobile API
 * Mobile Backend for Frontend API service
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { GenericChargeStateImpl } from './genericChargeStateImpl';
import { VehicleInformationImpl } from './vehicleInformationImpl';
import { InterventionDtoImpl } from './interventionDtoImpl';

export interface SmartChargingControllerUpdateVehicle200Response {
  /**
   * Id
   */
  id: string;
  /**
   * Last seen date
   */
  lastSeen?: string | null;
  /**
   * The user\'s Enode ID
   */
  enodeUserId?: string | null;
  /**
   * The vehicles\'s Enode ID
   */
  enodeVehicleId?: string | null;
  /**
   * Vehicle data
   */
  vehicleInformation: VehicleInformationImpl;
  /**
   * Vehicle charge state data
   */
  chargeState: GenericChargeStateImpl;
  /**
   * Vehicle interventions
   */
  interventions: InterventionDtoImpl;
}
