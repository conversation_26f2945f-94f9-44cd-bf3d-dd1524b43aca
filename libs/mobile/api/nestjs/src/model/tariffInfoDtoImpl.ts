/**
 * Mobile API
 * Mobile Backend for Frontend API service
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

export interface TariffInfoDtoImpl {
  days: TariffInfoDtoImpl.DaysEnum[];
  /**
   * When the period starts (format: XX:XX:XX)
   */
  start: string;
  /**
   * When the period ends (format: XX:XX:XX)
   */
  end: string;
  /**
   * Price per unit (in pence)
   */
  price: number;
}
export namespace TariffInfoDtoImpl {
  export type DaysEnum =
    | 'MONDAY'
    | 'TUESDAY'
    | 'WEDNESDAY'
    | 'THURSDAY'
    | 'FRIDAY'
    | 'SATURDAY'
    | 'SUNDAY';
  export const DaysEnum = {
    Monday: 'MONDAY' as DaysEnum,
    Tuesday: 'TUESDAY' as DaysEnum,
    Wednesday: 'WEDNESDAY' as DaysEnum,
    Thursday: 'THURSDAY' as DaysEnum,
    Friday: 'FRIDAY' as DaysEnum,
    Saturday: 'SATURDAY' as DaysEnum,
    Sunday: 'SUNDAY' as DaysEnum,
  };
}
