/**
 * Mobile API
 * Mobile Backend for Frontend API service
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { TariffInfoDtoImpl } from './tariffInfoDtoImpl';

export interface SupplierDtoImpl {
  id: string;
  name: string;
  timeZone: SupplierDtoImpl.TimeZoneEnum;
  icon: string;
  defaultMaxChargePrice: number;
  defaultTariffInfo: TariffInfoDtoImpl[];
}
export namespace SupplierDtoImpl {
  export type TimeZoneEnum =
    | 'Europe/London'
    | 'Europe/Madrid'
    | 'Europe/Paris'
    | 'Etc/UTC';
  export const TimeZoneEnum = {
    EuropeLondon: 'Europe/London' as TimeZoneEnum,
    EuropeMadrid: 'Europe/Madrid' as TimeZoneEnum,
    EuropeParis: 'Europe/Paris' as TimeZoneEnum,
    EtcUtc: 'Etc/UTC' as TimeZoneEnum,
  };
}
