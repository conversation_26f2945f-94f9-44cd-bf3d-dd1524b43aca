/**
 * Mobile API
 * Mobile Backend for Frontend API service
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { Entry } from './entry';

export interface Forecastdata {
  /**
   * The DNO region\'s ID.
   */
  regionid: number;
  /**
   * The DNO region.
   */
  dnoregion: string;
  /**
   * The DNO region\'s shortname.
   */
  shortname: string;
  /**
   * List of carbon intensity for 30min periods.
   */
  dates: Entry[];
}
