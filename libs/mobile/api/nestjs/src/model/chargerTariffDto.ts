/**
 * Mobile API
 * Mobile Backend for Frontend API service
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { TariffInfoDtoImpl } from './tariffInfoDtoImpl';

export interface ChargerTariffDto {
  /**
   * The ID of the tariff
   */
  id: string;
  /**
   * The date from which the tariff is effective (inclusive)
   */
  effectiveFrom: string;
  /**
   * The maximum price during off-peak hours in pence
   */
  maxChargePrice?: number | null;
  /**
   * The PPID of the charger related to the tariff
   */
  ppid: string;
  /**
   * Reference to the supplier. Null if the supplier is unknown
   */
  supplierId: string | null;
  /**
   * Tariff information applicable to specific days and times.
   */
  tariffInfo: TariffInfoDtoImpl[];
  /**
   * Timezone the tariff information applies to
   */
  timezone: string;
  /**
   * The cheapest unit price (e.g., £0.10).
   */
  cheapestUnitPrice: number;
}
