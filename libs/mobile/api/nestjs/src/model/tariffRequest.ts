/**
 * Mobile API
 * Mobile Backend for Frontend API service
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { Tier } from './tier';

export interface TariffRequest {
  /**
   * User Id
   */
  user_id: number;
  /**
   * Energy supplier id
   */
  energy_supplier_id: number | null;
  /**
   * A list of tarrifs
   */
  tiers: Tier[];
}
