/**
 * Mobile API
 * Mobile Backend for Frontend API service
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { TariffInfoDtoImpl } from './tariffInfoDtoImpl';

export interface SetChargerTariffDto {
  /**
   * The date from which the tariff is effective (inclusive)
   */
  effectiveFrom: string;
  /**
   * The maximum price during off-peak hours in pence
   */
  maxChargePrice?: number | null;
  /**
   * Reference to the supplier. Null if the supplier is unknown
   */
  supplierId: string | null;
  /**
   * Tariff information applicable to specific days and times.
   */
  tariffInfo: TariffInfoDtoImpl[];
  /**
   * Timezone the tariff information applies to
   */
  timezone: string;
}
