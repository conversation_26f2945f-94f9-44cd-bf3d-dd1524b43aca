/**
 * Mobile API
 * Mobile Backend for Frontend API service
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { VehicleLinkRequestDtoImpl } from './vehicleLinkRequestDtoImpl';

export interface DelegatedControlChargingStationResponseDtoImpl {
  /**
   * Id
   */
  id: string;
  /**
   * Pod Point id
   */
  ppid: string;
  status?: DelegatedControlChargingStationResponseDtoImpl.StatusEnum;
  /**
   * Created at
   */
  createdAt: string;
  /**
   * Vehicle links
   */
  vehicleLinks?: VehicleLinkRequestDtoImpl[];
}
export namespace DelegatedControlChargingStationResponseDtoImpl {
  export type StatusEnum = 'UNKNOWN' | 'ACTIVE' | 'INACTIVE' | 'PENDING';
  export const StatusEnum = {
    Unknown: 'UNKNOWN' as StatusEnum,
    Active: 'ACTIVE' as StatusEnum,
    Inactive: 'INACTIVE' as StatusEnum,
    Pending: 'PENDING' as StatusEnum,
  };
}
