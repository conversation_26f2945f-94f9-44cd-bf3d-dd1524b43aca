/**
 * Mobile API
 * Mobile Backend for Frontend API service
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

export interface Restrictions {
  /**
   * Boolean for whether the user is allowed to charge
   */
  chargeAllowed: boolean;
  /**
   * Minimum balance required to use the charger
   */
  minimumBalance: object;
  /**
   * The user\'s current balance
   */
  userBalance: object;
  /**
   * The amount of charge that can be used before stopping
   */
  chargeLimits?: string[];
}
