/**
 * Mobile API
 * Mobile Backend for Frontend API service
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

export interface MarketingOpportunitiesDTO {
  /**
   * A list of all the marketing opportunities a charger is eligible for
   */
  opportunities: MarketingOpportunitiesDTO.OpportunitiesEnum[];
}
export namespace MarketingOpportunitiesDTO {
  export type OpportunitiesEnum = 'REWARDS' | 'TARIFF' | 'MIGRATE';
  export const OpportunitiesEnum = {
    Rewards: 'REWARDS' as OpportunitiesEnum,
    Tariff: 'TARIFF' as OpportunitiesEnum,
    Migrate: 'MIGRATE' as OpportunitiesEnum,
  };
}
