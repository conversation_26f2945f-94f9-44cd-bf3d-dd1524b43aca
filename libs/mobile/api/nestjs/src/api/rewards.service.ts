/**
 * Mobile API
 * Mobile Backend for Frontend API service
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
/* tslint:disable:no-unused-variable member-ordering */

import { Injectable, Optional } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { AxiosResponse } from 'axios';
import { Observable, from, of, switchMap } from 'rxjs';
import { PayoutRequestDTOImpl } from '../model/payoutRequestDTOImpl';
import { PayoutResponseDTOImpl } from '../model/payoutResponseDTOImpl';
import { RewardsAccountDTO } from '../model/rewardsAccountDTO';
import { RewardsBankAccountDTOImpl } from '../model/rewardsBankAccountDTOImpl';
import { RewardsTransactionDTOImpl } from '../model/rewardsTransactionDTOImpl';
import { Configuration } from '../configuration';

@Injectable()
export class RewardsService {
  protected basePath = 'http://localhost';
  public defaultHeaders: Record<string, string> = {};
  public configuration = new Configuration();
  protected httpClient: HttpService;

  constructor(
    httpClient: HttpService,
    @Optional() configuration: Configuration
  ) {
    this.configuration = configuration || this.configuration;
    this.basePath = configuration?.basePath || this.basePath;
    this.httpClient = configuration?.httpClient || httpClient;
  }

  /**
   * @param consumes string[] mime-types
   * @return true: consumes contains 'multipart/form-data', false: otherwise
   */
  private canConsumeForm(consumes: string[]): boolean {
    const form = 'multipart/form-data';
    return consumes.includes(form);
  }

  /**
   *
   * Archive a users bank account
   * @param bankAccountId The ID of the bank account
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public rewardsControllerArchiveAccount(
    bankAccountId: string
  ): Observable<AxiosResponse<any>>;
  public rewardsControllerArchiveAccount(
    bankAccountId: string
  ): Observable<any> {
    if (bankAccountId === null || bankAccountId === undefined) {
      throw new Error(
        'Required parameter bankAccountId was null or undefined when calling rewardsControllerArchiveAccount.'
      );
    }

    const headers = { ...this.defaultHeaders };

    let accessTokenObservable: Observable<any> = of(null);

    // authentication (bearer) required
    if (typeof this.configuration.accessToken === 'function') {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken())
      );
    } else if (this.configuration.accessToken) {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken)
      );
    }
    // to determine the Accept header
    const httpHeaderAccepts: string[] = ['application/json'];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = [];
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.delete<any>(
          `${this.basePath}/rewards/bank-accounts/${encodeURIComponent(
            String(bankAccountId)
          )}`,
          {
            withCredentials: this.configuration.withCredentials,
            headers: headers,
          }
        );
      })
    );
  }
  /**
   *
   * Create a new rewards bank account for the authenticated user
   * @param rewardsAccountDTO
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public rewardsControllerCreateBankAccount(
    rewardsAccountDTO: RewardsAccountDTO
  ): Observable<AxiosResponse<RewardsBankAccountDTOImpl>>;
  public rewardsControllerCreateBankAccount(
    rewardsAccountDTO: RewardsAccountDTO
  ): Observable<any> {
    if (rewardsAccountDTO === null || rewardsAccountDTO === undefined) {
      throw new Error(
        'Required parameter rewardsAccountDTO was null or undefined when calling rewardsControllerCreateBankAccount.'
      );
    }

    const headers = { ...this.defaultHeaders };

    let accessTokenObservable: Observable<any> = of(null);

    // authentication (bearer) required
    if (typeof this.configuration.accessToken === 'function') {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken())
      );
    } else if (this.configuration.accessToken) {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken)
      );
    }
    // to determine the Accept header
    const httpHeaderAccepts: string[] = ['application/json'];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = ['application/json'];
    const httpContentTypeSelected: string | undefined =
      this.configuration.selectHeaderContentType(consumes);
    if (httpContentTypeSelected != undefined) {
      headers['Content-Type'] = httpContentTypeSelected;
    }
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.post<RewardsBankAccountDTOImpl>(
          `${this.basePath}/rewards/bank-accounts`,
          rewardsAccountDTO,
          {
            withCredentials: this.configuration.withCredentials,
            headers: headers,
          }
        );
      })
    );
  }
  /**
   *
   * Get the user\&#39;s bank accounts
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public rewardsControllerGetBankAccounts(): Observable<
    AxiosResponse<RewardsBankAccountDTOImpl[]>
  >;
  public rewardsControllerGetBankAccounts(): Observable<any> {
    const headers = { ...this.defaultHeaders };

    let accessTokenObservable: Observable<any> = of(null);

    // authentication (bearer) required
    if (typeof this.configuration.accessToken === 'function') {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken())
      );
    } else if (this.configuration.accessToken) {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken)
      );
    }
    // to determine the Accept header
    const httpHeaderAccepts: string[] = ['application/json'];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = [];
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.get<RewardsBankAccountDTOImpl[]>(
          `${this.basePath}/rewards/bank-accounts`,
          {
            withCredentials: this.configuration.withCredentials,
            headers: headers,
          }
        );
      })
    );
  }
  /**
   *
   * Get a list of rewards-related transactions the user has made
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public rewardsControllerGetRewardsTransactions(): Observable<
    AxiosResponse<RewardsTransactionDTOImpl[]>
  >;
  public rewardsControllerGetRewardsTransactions(): Observable<any> {
    const headers = { ...this.defaultHeaders };

    let accessTokenObservable: Observable<any> = of(null);

    // authentication (bearer) required
    if (typeof this.configuration.accessToken === 'function') {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken())
      );
    } else if (this.configuration.accessToken) {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken)
      );
    }
    // to determine the Accept header
    const httpHeaderAccepts: string[] = ['application/json'];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = [];
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.get<RewardsTransactionDTOImpl[]>(
          `${this.basePath}/rewards/transactions`,
          {
            withCredentials: this.configuration.withCredentials,
            headers: headers,
          }
        );
      })
    );
  }
  /**
   *
   * Make a payout of the rewards balance to the user
   * @param payoutRequestDTOImpl
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public rewardsControllerMakeRewardsPayout(
    payoutRequestDTOImpl: PayoutRequestDTOImpl
  ): Observable<AxiosResponse<PayoutResponseDTOImpl>>;
  public rewardsControllerMakeRewardsPayout(
    payoutRequestDTOImpl: PayoutRequestDTOImpl
  ): Observable<any> {
    if (payoutRequestDTOImpl === null || payoutRequestDTOImpl === undefined) {
      throw new Error(
        'Required parameter payoutRequestDTOImpl was null or undefined when calling rewardsControllerMakeRewardsPayout.'
      );
    }

    const headers = { ...this.defaultHeaders };

    let accessTokenObservable: Observable<any> = of(null);

    // authentication (bearer) required
    if (typeof this.configuration.accessToken === 'function') {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken())
      );
    } else if (this.configuration.accessToken) {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken)
      );
    }
    // to determine the Accept header
    const httpHeaderAccepts: string[] = ['application/json'];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = ['application/json'];
    const httpContentTypeSelected: string | undefined =
      this.configuration.selectHeaderContentType(consumes);
    if (httpContentTypeSelected != undefined) {
      headers['Content-Type'] = httpContentTypeSelected;
    }
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.post<PayoutResponseDTOImpl>(
          `${this.basePath}/rewards/payout`,
          payoutRequestDTOImpl,
          {
            withCredentials: this.configuration.withCredentials,
            headers: headers,
          }
        );
      })
    );
  }
  /**
   *
   *
   * @param stripeSignature
   * @param body
   * @param stripeSignature2 Used for verifying the request
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public rewardsControllerProxyWebhook(
    stripeSignature: string,
    body: object,
    stripeSignature2?: string
  ): Observable<AxiosResponse<any>>;
  public rewardsControllerProxyWebhook(
    stripeSignature: string,
    body: object,
    stripeSignature2?: string
  ): Observable<any> {
    if (stripeSignature === null || stripeSignature === undefined) {
      throw new Error(
        'Required parameter stripeSignature was null or undefined when calling rewardsControllerProxyWebhook.'
      );
    }

    if (body === null || body === undefined) {
      throw new Error(
        'Required parameter body was null or undefined when calling rewardsControllerProxyWebhook.'
      );
    }

    const headers = { ...this.defaultHeaders };
    if (stripeSignature !== undefined && stripeSignature !== null) {
      headers['stripe-signature'] = String(stripeSignature);
    }
    if (stripeSignature2 !== undefined && stripeSignature2 !== null) {
      headers['Stripe-Signature'] = String(stripeSignature2);
    }

    let accessTokenObservable: Observable<any> = of(null);

    // authentication (bearer) required
    if (typeof this.configuration.accessToken === 'function') {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken())
      );
    } else if (this.configuration.accessToken) {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken)
      );
    }
    // to determine the Accept header
    const httpHeaderAccepts: string[] = ['application/json'];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = ['application/json'];
    const httpContentTypeSelected: string | undefined =
      this.configuration.selectHeaderContentType(consumes);
    if (httpContentTypeSelected != undefined) {
      headers['Content-Type'] = httpContentTypeSelected;
    }
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.post<any>(
          `${this.basePath}/rewards/webhook`,
          body,
          {
            withCredentials: this.configuration.withCredentials,
            headers: headers,
          }
        );
      })
    );
  }
  /**
   *
   * Update a users bank account
   * @param bankAccountId The ID of the bank account
   * @param rewardsAccountDTO
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public rewardsControllerUpdateBankAccount(
    bankAccountId: string,
    rewardsAccountDTO: RewardsAccountDTO
  ): Observable<AxiosResponse<any>>;
  public rewardsControllerUpdateBankAccount(
    bankAccountId: string,
    rewardsAccountDTO: RewardsAccountDTO
  ): Observable<any> {
    if (bankAccountId === null || bankAccountId === undefined) {
      throw new Error(
        'Required parameter bankAccountId was null or undefined when calling rewardsControllerUpdateBankAccount.'
      );
    }

    if (rewardsAccountDTO === null || rewardsAccountDTO === undefined) {
      throw new Error(
        'Required parameter rewardsAccountDTO was null or undefined when calling rewardsControllerUpdateBankAccount.'
      );
    }

    const headers = { ...this.defaultHeaders };

    let accessTokenObservable: Observable<any> = of(null);

    // authentication (bearer) required
    if (typeof this.configuration.accessToken === 'function') {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken())
      );
    } else if (this.configuration.accessToken) {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken)
      );
    }
    // to determine the Accept header
    const httpHeaderAccepts: string[] = ['application/json'];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = ['application/json'];
    const httpContentTypeSelected: string | undefined =
      this.configuration.selectHeaderContentType(consumes);
    if (httpContentTypeSelected != undefined) {
      headers['Content-Type'] = httpContentTypeSelected;
    }
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.put<any>(
          `${this.basePath}/rewards/bank-accounts/${encodeURIComponent(
            String(bankAccountId)
          )}`,
          rewardsAccountDTO,
          {
            withCredentials: this.configuration.withCredentials,
            headers: headers,
          }
        );
      })
    );
  }
}
