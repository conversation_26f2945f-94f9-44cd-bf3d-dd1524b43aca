/**
 * Mobile API
 * Mobile Backend for Frontend API service
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
/* tslint:disable:no-unused-variable member-ordering */

import { Injectable, Optional } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { AxiosResponse } from 'axios';
import { Observable, from, of, switchMap } from 'rxjs';
import { DnoRegions } from '../model/dnoRegions';
import { Forecast } from '../model/forecast';
import { ForecastSnapshot } from '../model/forecastSnapshot';
import { Configuration } from '../configuration';

@Injectable()
export class CarbonIntensityService {
  protected basePath = 'http://localhost';
  public defaultHeaders: Record<string, string> = {};
  public configuration = new Configuration();
  protected httpClient: HttpService;

  constructor(
    httpClient: HttpService,
    @Optional() configuration: Configuration
  ) {
    this.configuration = configuration || this.configuration;
    this.basePath = configuration?.basePath || this.basePath;
    this.httpClient = configuration?.httpClient || httpClient;
  }

  /**
   * @param consumes string[] mime-types
   * @return true: consumes contains 'multipart/form-data', false: otherwise
   */
  private canConsumeForm(consumes: string[]): boolean {
    const form = 'multipart/form-data';
    return consumes.includes(form);
  }

  /**
   * retrieve regional forecast for a 30minute window that includes the provided date
   * Retrieve, half-hourly, forecast data that includes the provided date
   * @param _from Datetime is in ISO8601 and RFC3339 compliant format YYYY-MM-DDThh:mm:ssZ.
   * @param regionId Region ID of GB region. See list of Region IDs here: https://carbon-intensity.github.io/api-definitions/#region-list.
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public carbonControllerGetCarbonIntensity30m(
    _from: string,
    regionId: number
  ): Observable<AxiosResponse<ForecastSnapshot>>;
  public carbonControllerGetCarbonIntensity30m(
    _from: string,
    regionId: number
  ): Observable<any> {
    if (_from === null || _from === undefined) {
      throw new Error(
        'Required parameter _from was null or undefined when calling carbonControllerGetCarbonIntensity30m.'
      );
    }

    if (regionId === null || regionId === undefined) {
      throw new Error(
        'Required parameter regionId was null or undefined when calling carbonControllerGetCarbonIntensity30m.'
      );
    }

    const headers = { ...this.defaultHeaders };

    let accessTokenObservable: Observable<any> = of(null);

    // authentication (bearer) required
    if (typeof this.configuration.accessToken === 'function') {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken())
      );
    } else if (this.configuration.accessToken) {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken)
      );
    }
    // to determine the Accept header
    const httpHeaderAccepts: string[] = ['application/json'];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = [];
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.get<ForecastSnapshot>(
          `${this.basePath}/carbon/intensity/${encodeURIComponent(
            String(_from.toISOString())
          )}/fw30m/regionid/${encodeURIComponent(String(regionId))}`,
          {
            withCredentials: this.configuration.withCredentials,
            headers: headers,
          }
        );
      })
    );
  }
  /**
   * retrieve regional forecast 48 hours from date
   * Retrieve, half-hourly, forecast data 48 hours from provided date
   * @param _from Datetime is in ISO8601 and RFC3339 compliant format YYYY-MM-DDThh:mm:ssZ.
   * @param regionId Region ID of GB region. See list of Region IDs here: https://carbon-intensity.github.io/api-definitions/#region-list.
   * @param timezone Timezone in which to normalise date transformation, as an IANA timezone name. Defaults to Etc/UTC. See list of time zones here: https://en.wikipedia.org/wiki/List_of_tz_database_time_zones.
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public carbonControllerGetCarbonIntensity48h(
    _from: string,
    regionId: number,
    timezone?: any
  ): Observable<AxiosResponse<Forecast>>;
  public carbonControllerGetCarbonIntensity48h(
    _from: string,
    regionId: number,
    timezone?: any
  ): Observable<any> {
    if (_from === null || _from === undefined) {
      throw new Error(
        'Required parameter _from was null or undefined when calling carbonControllerGetCarbonIntensity48h.'
      );
    }

    if (regionId === null || regionId === undefined) {
      throw new Error(
        'Required parameter regionId was null or undefined when calling carbonControllerGetCarbonIntensity48h.'
      );
    }

    const queryParameters = new URLSearchParams();
    if (timezone !== undefined && timezone !== null) {
      queryParameters.append('timezone', <any>timezone);
    }

    const headers = { ...this.defaultHeaders };

    let accessTokenObservable: Observable<any> = of(null);

    // authentication (bearer) required
    if (typeof this.configuration.accessToken === 'function') {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken())
      );
    } else if (this.configuration.accessToken) {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken)
      );
    }
    // to determine the Accept header
    const httpHeaderAccepts: string[] = ['application/json'];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = [];
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.get<Forecast>(
          `${this.basePath}/carbon/intensity/${encodeURIComponent(
            String(_from.toISOString())
          )}/fw48h/regionid/${encodeURIComponent(String(regionId))}`,
          {
            params: queryParameters,
            withCredentials: this.configuration.withCredentials,
            headers: headers,
          }
        );
      })
    );
  }
  /**
   * get the list of DNO regions
   * Returns a list of DNO regions.
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public carbonControllerGetDnoRegions(): Observable<AxiosResponse<DnoRegions>>;
  public carbonControllerGetDnoRegions(): Observable<any> {
    const headers = { ...this.defaultHeaders };

    let accessTokenObservable: Observable<any> = of(null);

    // authentication (bearer) required
    if (typeof this.configuration.accessToken === 'function') {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken())
      );
    } else if (this.configuration.accessToken) {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken)
      );
    }
    // to determine the Accept header
    const httpHeaderAccepts: string[] = ['application/json'];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = [];
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.get<DnoRegions>(
          `${this.basePath}/carbon/regions`,
          {
            withCredentials: this.configuration.withCredentials,
            headers: headers,
          }
        );
      })
    );
  }
}
