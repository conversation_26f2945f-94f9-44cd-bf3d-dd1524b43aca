/**
 * Mobile API
 * Mobile Backend for Frontend API service
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
/* tslint:disable:no-unused-variable member-ordering */

import { Injectable, Optional } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { AxiosResponse } from 'axios';
import { Observable, from, of, switchMap } from 'rxjs';
import { ChargeStatsResponse } from '../model/chargeStatsResponse';
import { ChargesResponse } from '../model/chargesResponse';
import { Configuration } from '../configuration';

@Injectable()
export class ChargesService {
  protected basePath = 'http://localhost';
  public defaultHeaders: Record<string, string> = {};
  public configuration = new Configuration();
  protected httpClient: HttpService;

  constructor(
    httpClient: HttpService,
    @Optional() configuration: Configuration
  ) {
    this.configuration = configuration || this.configuration;
    this.basePath = configuration?.basePath || this.basePath;
    this.httpClient = configuration?.httpClient || httpClient;
  }

  /**
   * @param consumes string[] mime-types
   * @return true: consumes contains 'multipart/form-data', false: otherwise
   */
  private canConsumeForm(consumes: string[]): boolean {
    const form = 'multipart/form-data';
    return consumes.includes(form);
  }

  /**
   * retrieve charger sessions
   * For given dates it should return charger sessions.
   * @param _from Statistics report inclusive start date eg: 2022-01-01
   * @param to Statistics report inclusive end date eg: 2022-01-02
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public chargesControllerGetCharges(
    _from: string,
    to: string
  ): Observable<AxiosResponse<ChargesResponse>>;
  public chargesControllerGetCharges(
    _from: string,
    to: string
  ): Observable<any> {
    if (_from === null || _from === undefined) {
      throw new Error(
        'Required parameter _from was null or undefined when calling chargesControllerGetCharges.'
      );
    }

    if (to === null || to === undefined) {
      throw new Error(
        'Required parameter to was null or undefined when calling chargesControllerGetCharges.'
      );
    }

    const queryParameters = new URLSearchParams();
    if (_from !== undefined && _from !== null) {
      queryParameters.append('from', <any>_from);
    }
    if (to !== undefined && to !== null) {
      queryParameters.append('to', <any>to);
    }

    const headers = { ...this.defaultHeaders };

    let accessTokenObservable: Observable<any> = of(null);

    // authentication (bearer) required
    if (typeof this.configuration.accessToken === 'function') {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken())
      );
    } else if (this.configuration.accessToken) {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken)
      );
    }
    // to determine the Accept header
    const httpHeaderAccepts: string[] = ['application/json'];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = [];
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.get<ChargesResponse>(
          `${this.basePath}/charges`,
          {
            params: queryParameters,
            withCredentials: this.configuration.withCredentials,
            headers: headers,
          }
        );
      })
    );
  }
  /**
   * retrieve charger stats
   * For given dates and inteval it should return charger stats.
   * @param interval Time duration interval data should be provided in.
   * @param _from Statistics report inclusive start date eg: 2022-01-01
   * @param to Statistics report inclusive end date eg: 2022-01-02
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public chargesControllerGetChargesStats(
    interval: 'day' | 'week' | 'month',
    _from?: string,
    to?: string
  ): Observable<AxiosResponse<ChargeStatsResponse>>;
  public chargesControllerGetChargesStats(
    interval: 'day' | 'week' | 'month',
    _from?: string,
    to?: string
  ): Observable<any> {
    if (interval === null || interval === undefined) {
      throw new Error(
        'Required parameter interval was null or undefined when calling chargesControllerGetChargesStats.'
      );
    }

    const queryParameters = new URLSearchParams();
    if (_from !== undefined && _from !== null) {
      queryParameters.append('from', <any>_from);
    }
    if (to !== undefined && to !== null) {
      queryParameters.append('to', <any>to);
    }
    if (interval !== undefined && interval !== null) {
      queryParameters.append('interval', <any>interval);
    }

    const headers = { ...this.defaultHeaders };

    let accessTokenObservable: Observable<any> = of(null);

    // authentication (bearer) required
    if (typeof this.configuration.accessToken === 'function') {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken())
      );
    } else if (this.configuration.accessToken) {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken)
      );
    }
    // to determine the Accept header
    const httpHeaderAccepts: string[] = ['application/json'];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = [];
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.get<ChargeStatsResponse>(
          `${this.basePath}/charges/stats`,
          {
            params: queryParameters,
            withCredentials: this.configuration.withCredentials,
            headers: headers,
          }
        );
      })
    );
  }
}
