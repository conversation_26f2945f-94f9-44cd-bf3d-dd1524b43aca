/**
 * Mobile API
 * Mobile Backend for Frontend API service
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
/* tslint:disable:no-unused-variable member-ordering */

import { Injectable, Optional } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { AxiosResponse } from 'axios';
import { Observable, of, switchMap } from 'rxjs';
import { SubmitSupportFeedbackDTO } from '../model/submitSupportFeedbackDTO';
import { Configuration } from '../configuration';

@Injectable()
export class SupportService {
  protected basePath = 'http://localhost';
  public defaultHeaders: Record<string, string> = {};
  public configuration = new Configuration();
  protected httpClient: HttpService;

  constructor(
    httpClient: HttpService,
    @Optional() configuration: Configuration
  ) {
    this.configuration = configuration || this.configuration;
    this.basePath = configuration?.basePath || this.basePath;
    this.httpClient = configuration?.httpClient || httpClient;
  }

  /**
   * @param consumes string[] mime-types
   * @return true: consumes contains 'multipart/form-data', false: otherwise
   */
  private canConsumeForm(consumes: string[]): boolean {
    const form = 'multipart/form-data';
    return consumes.includes(form);
  }

  /**
   * submit feedback to support
   * Sends an email to the language-specific support team
   * @param acceptLanguage
   * @param submitSupportFeedbackDTO
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public supportControllerSubmitFeedback(
    acceptLanguage: string,
    submitSupportFeedbackDTO: SubmitSupportFeedbackDTO
  ): Observable<AxiosResponse<any>>;
  public supportControllerSubmitFeedback(
    acceptLanguage: string,
    submitSupportFeedbackDTO: SubmitSupportFeedbackDTO
  ): Observable<any> {
    if (acceptLanguage === null || acceptLanguage === undefined) {
      throw new Error(
        'Required parameter acceptLanguage was null or undefined when calling supportControllerSubmitFeedback.'
      );
    }

    if (
      submitSupportFeedbackDTO === null ||
      submitSupportFeedbackDTO === undefined
    ) {
      throw new Error(
        'Required parameter submitSupportFeedbackDTO was null or undefined when calling supportControllerSubmitFeedback.'
      );
    }

    const headers = { ...this.defaultHeaders };
    if (acceptLanguage !== undefined && acceptLanguage !== null) {
      headers['Accept-Language'] = String(acceptLanguage);
    }

    const accessTokenObservable: Observable<any> = of(null);

    // to determine the Accept header
    const httpHeaderAccepts: string[] = [];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = ['application/json'];
    const httpContentTypeSelected: string | undefined =
      this.configuration.selectHeaderContentType(consumes);
    if (httpContentTypeSelected != undefined) {
      headers['Content-Type'] = httpContentTypeSelected;
    }
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.post<any>(
          `${this.basePath}/support/feedback`,
          submitSupportFeedbackDTO,
          {
            withCredentials: this.configuration.withCredentials,
            headers: headers,
          }
        );
      })
    );
  }
}
