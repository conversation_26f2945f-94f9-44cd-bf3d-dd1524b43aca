/**
 * Mobile API
 * Mobile Backend for Frontend API service
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
/* tslint:disable:no-unused-variable member-ordering */

import { Injectable, Optional } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { AxiosResponse } from 'axios';
import { Observable, from, of, switchMap } from 'rxjs';
import { ExpensesRequestBody } from '../model/expensesRequestBody';
import { ExpensesResponse } from '../model/expensesResponse';
import { Configuration } from '../configuration';

@Injectable()
export class ExpensesService {
  protected basePath = 'http://localhost';
  public defaultHeaders: Record<string, string> = {};
  public configuration = new Configuration();
  protected httpClient: HttpService;

  constructor(
    httpClient: HttpService,
    @Optional() configuration: Configuration
  ) {
    this.configuration = configuration || this.configuration;
    this.basePath = configuration?.basePath || this.basePath;
    this.httpClient = configuration?.httpClient || httpClient;
  }

  /**
   * @param consumes string[] mime-types
   * @return true: consumes contains 'multipart/form-data', false: otherwise
   */
  private canConsumeForm(consumes: string[]): boolean {
    const form = 'multipart/form-data';
    return consumes.includes(form);
  }

  /**
   * Submitting a list of charges as expenses for a driver within a group.
   * Submitting a list of charges as expenses for a driver within a group.
   * @param organisationId
   * @param expensesRequestBody
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public expensesControllerExpenseChargeTo(
    organisationId: number,
    expensesRequestBody: ExpensesRequestBody
  ): Observable<AxiosResponse<ExpensesResponse>>;
  public expensesControllerExpenseChargeTo(
    organisationId: number,
    expensesRequestBody: ExpensesRequestBody
  ): Observable<any> {
    if (organisationId === null || organisationId === undefined) {
      throw new Error(
        'Required parameter organisationId was null or undefined when calling expensesControllerExpenseChargeTo.'
      );
    }

    if (expensesRequestBody === null || expensesRequestBody === undefined) {
      throw new Error(
        'Required parameter expensesRequestBody was null or undefined when calling expensesControllerExpenseChargeTo.'
      );
    }

    const headers = { ...this.defaultHeaders };

    let accessTokenObservable: Observable<any> = of(null);

    // authentication (bearer) required
    if (typeof this.configuration.accessToken === 'function') {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken())
      );
    } else if (this.configuration.accessToken) {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken)
      );
    }
    // to determine the Accept header
    const httpHeaderAccepts: string[] = ['application/json'];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = ['application/json'];
    const httpContentTypeSelected: string | undefined =
      this.configuration.selectHeaderContentType(consumes);
    if (httpContentTypeSelected != undefined) {
      headers['Content-Type'] = httpContentTypeSelected;
    }
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.post<ExpensesResponse>(
          `${this.basePath}/expenses/groups/${encodeURIComponent(
            String(organisationId)
          )}`,
          expensesRequestBody,
          {
            withCredentials: this.configuration.withCredentials,
            headers: headers,
          }
        );
      })
    );
  }
}
