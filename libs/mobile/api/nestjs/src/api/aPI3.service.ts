/**
 * Mobile API
 * Mobile Backend for Frontend API service
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
/* tslint:disable:no-unused-variable member-ordering */

import { Injectable, Optional } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { AxiosResponse } from 'axios';
import { Observable, from, of, switchMap } from 'rxjs';
import { AccountTopUpRequestDTO } from '../model/accountTopUpRequestDTO';
import { ChargeRequestDTO } from '../model/chargeRequestDTO';
import { ChargeRequestResponse } from '../model/chargeRequestResponse';
import { FirmwareStatusResponse } from '../model/firmwareStatusResponse';
import { LocaleResponse } from '../model/localeResponse';
import { TariffRequest } from '../model/tariffRequest';
import { TariffResponse } from '../model/tariffResponse';
import { UserResponse } from '../model/userResponse';
import { Configuration } from '../configuration';

@Injectable()
export class API3Service {
  protected basePath = 'http://localhost';
  public defaultHeaders: Record<string, string> = {};
  public configuration = new Configuration();
  protected httpClient: HttpService;

  constructor(
    httpClient: HttpService,
    @Optional() configuration: Configuration
  ) {
    this.configuration = configuration || this.configuration;
    this.basePath = configuration?.basePath || this.basePath;
    this.httpClient = configuration?.httpClient || httpClient;
  }

  /**
   * @param consumes string[] mime-types
   * @return true: consumes contains 'multipart/form-data', false: otherwise
   */
  private canConsumeForm(consumes: string[]): boolean {
    const form = 'multipart/form-data';
    return consumes.includes(form);
  }

  /**
   * claim a charge
   * Claims a given charge
   * @param chargeRequestDTO
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public api3ControllerClaimCharge(
    chargeRequestDTO: ChargeRequestDTO
  ): Observable<AxiosResponse<ChargeRequestResponse>>;
  public api3ControllerClaimCharge(
    chargeRequestDTO: ChargeRequestDTO
  ): Observable<any> {
    if (chargeRequestDTO === null || chargeRequestDTO === undefined) {
      throw new Error(
        'Required parameter chargeRequestDTO was null or undefined when calling api3ControllerClaimCharge.'
      );
    }

    const headers = { ...this.defaultHeaders };

    let accessTokenObservable: Observable<any> = of(null);

    // authentication (bearer) required
    if (typeof this.configuration.accessToken === 'function') {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken())
      );
    } else if (this.configuration.accessToken) {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken)
      );
    }
    // to determine the Accept header
    const httpHeaderAccepts: string[] = ['application/json'];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = ['application/json'];
    const httpContentTypeSelected: string | undefined =
      this.configuration.selectHeaderContentType(consumes);
    if (httpContentTypeSelected != undefined) {
      headers['Content-Type'] = httpContentTypeSelected;
    }
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.post<ChargeRequestResponse>(
          `${this.basePath}/api3/v5/charges`,
          chargeRequestDTO,
          {
            withCredentials: this.configuration.withCredentials,
            headers: headers,
          }
        );
      })
    );
  }
  /**
   * request firmware version
   * Request firmware version
   * @param unitId
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public api3ControllerGetCurrentFirmware(
    unitId: number
  ): Observable<AxiosResponse<FirmwareStatusResponse>>;
  public api3ControllerGetCurrentFirmware(unitId: number): Observable<any> {
    if (unitId === null || unitId === undefined) {
      throw new Error(
        'Required parameter unitId was null or undefined when calling api3ControllerGetCurrentFirmware.'
      );
    }

    const headers = { ...this.defaultHeaders };

    let accessTokenObservable: Observable<any> = of(null);

    // authentication (bearer) required
    if (typeof this.configuration.accessToken === 'function') {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken())
      );
    } else if (this.configuration.accessToken) {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken)
      );
    }
    // to determine the Accept header
    const httpHeaderAccepts: string[] = ['application/json'];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = [];
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.get<FirmwareStatusResponse>(
          `${this.basePath}/api3/v5/units/${encodeURIComponent(
            String(unitId)
          )}/firmware`,
          {
            withCredentials: this.configuration.withCredentials,
            headers: headers,
          }
        );
      })
    );
  }
  /**
   * get locales
   * Request to get all the locales
   * @param acceptLanguage Indicate the preferred language for the response (see https://www.rfc-editor.org/rfc/rfc9110#section-12.5.4)
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public api3ControllerGetLocales(
    acceptLanguage?: string
  ): Observable<AxiosResponse<LocaleResponse>>;
  public api3ControllerGetLocales(acceptLanguage?: string): Observable<any> {
    const headers = { ...this.defaultHeaders };
    if (acceptLanguage !== undefined && acceptLanguage !== null) {
      headers['Accept-Language'] = String(acceptLanguage);
    }

    let accessTokenObservable: Observable<any> = of(null);

    // authentication (bearer) required
    if (typeof this.configuration.accessToken === 'function') {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken())
      );
    } else if (this.configuration.accessToken) {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken)
      );
    }
    // to determine the Accept header
    const httpHeaderAccepts: string[] = ['application/json'];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = [];
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.get<LocaleResponse>(
          `${this.basePath}/api3/v5/locales`,
          {
            withCredentials: this.configuration.withCredentials,
            headers: headers,
          }
        );
      })
    );
  }
  /**
   * retrieve user info
   * Retrieve user info from API3 based on the JWT token
   * @param include Query param for auth
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public api3ControllerGetUser(
    include?: string
  ): Observable<AxiosResponse<UserResponse>>;
  public api3ControllerGetUser(include?: string): Observable<any> {
    const queryParameters = new URLSearchParams();
    if (include !== undefined && include !== null) {
      queryParameters.append('include', <any>include);
    }

    const headers = { ...this.defaultHeaders };

    let accessTokenObservable: Observable<any> = of(null);

    // authentication (bearer) required
    if (typeof this.configuration.accessToken === 'function') {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken())
      );
    } else if (this.configuration.accessToken) {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken)
      );
    }
    // to determine the Accept header
    const httpHeaderAccepts: string[] = ['application/json'];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = [];
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.get<UserResponse>(
          `${this.basePath}/api3/v5/auth`,
          {
            params: queryParameters,
            withCredentials: this.configuration.withCredentials,
            headers: headers,
          }
        );
      })
    );
  }
  /**
   * store tariff
   * Request to store a new tariff or update an existing one
   * @param tariffRequest
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public api3ControllerStoreTariff(
    tariffRequest: TariffRequest
  ): Observable<AxiosResponse<TariffResponse>>;
  public api3ControllerStoreTariff(
    tariffRequest: TariffRequest
  ): Observable<any> {
    if (tariffRequest === null || tariffRequest === undefined) {
      throw new Error(
        'Required parameter tariffRequest was null or undefined when calling api3ControllerStoreTariff.'
      );
    }

    const headers = { ...this.defaultHeaders };

    let accessTokenObservable: Observable<any> = of(null);

    // authentication (bearer) required
    if (typeof this.configuration.accessToken === 'function') {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken())
      );
    } else if (this.configuration.accessToken) {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken)
      );
    }
    // to determine the Accept header
    const httpHeaderAccepts: string[] = ['application/json'];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = ['application/json'];
    const httpContentTypeSelected: string | undefined =
      this.configuration.selectHeaderContentType(consumes);
    if (httpContentTypeSelected != undefined) {
      headers['Content-Type'] = httpContentTypeSelected;
    }
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.post<TariffResponse>(
          `${this.basePath}/api3/v5/tariffs`,
          tariffRequest,
          {
            withCredentials: this.configuration.withCredentials,
            headers: headers,
          }
        );
      })
    );
  }
  /**
   * top up an account
   * Tops up the user\&#39;s account
   * @param id
   * @param accountTopUpRequestDTO
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public api3ControllerTopUpAccount(
    id: string,
    accountTopUpRequestDTO: AccountTopUpRequestDTO
  ): Observable<AxiosResponse<any>>;
  public api3ControllerTopUpAccount(
    id: string,
    accountTopUpRequestDTO: AccountTopUpRequestDTO
  ): Observable<any> {
    if (id === null || id === undefined) {
      throw new Error(
        'Required parameter id was null or undefined when calling api3ControllerTopUpAccount.'
      );
    }

    if (
      accountTopUpRequestDTO === null ||
      accountTopUpRequestDTO === undefined
    ) {
      throw new Error(
        'Required parameter accountTopUpRequestDTO was null or undefined when calling api3ControllerTopUpAccount.'
      );
    }

    const headers = { ...this.defaultHeaders };

    let accessTokenObservable: Observable<any> = of(null);

    // authentication (bearer) required
    if (typeof this.configuration.accessToken === 'function') {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken())
      );
    } else if (this.configuration.accessToken) {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken)
      );
    }
    // to determine the Accept header
    const httpHeaderAccepts: string[] = ['application/json'];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = ['application/json'];
    const httpContentTypeSelected: string | undefined =
      this.configuration.selectHeaderContentType(consumes);
    if (httpContentTypeSelected != undefined) {
      headers['Content-Type'] = httpContentTypeSelected;
    }
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.post<any>(
          `${this.basePath}/api3/v5/users/${encodeURIComponent(
            String(id)
          )}/account/topup`,
          accountTopUpRequestDTO,
          {
            withCredentials: this.configuration.withCredentials,
            headers: headers,
          }
        );
      })
    );
  }
}
