/**
 * Mobile API
 * Mobile Backend for Frontend API service
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
/* tslint:disable:no-unused-variable member-ordering */

import { Injectable, Optional } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { AxiosResponse } from 'axios';
import { Observable, from, of, switchMap } from 'rxjs';
import { CreateUserPayload } from '../model/createUserPayload';
import { CreateUserResponseDto } from '../model/createUserResponseDto';
import { ResetPasswordRequestDto } from '../model/resetPasswordRequestDto';
import { SendRecoverFactorRequest } from '../model/sendRecoverFactorRequest';
import { SendVerifyAndChangeEmailRequest } from '../model/sendVerifyAndChangeEmailRequest';
import { UpdateUserDto } from '../model/updateUserDto';
import { UserDetailsDto } from '../model/userDetailsDto';
import { Configuration } from '../configuration';

@Injectable()
export class DriverAccountService {
  protected basePath = 'http://localhost';
  public defaultHeaders: Record<string, string> = {};
  public configuration = new Configuration();
  protected httpClient: HttpService;

  constructor(
    httpClient: HttpService,
    @Optional() configuration: Configuration
  ) {
    this.configuration = configuration || this.configuration;
    this.basePath = configuration?.basePath || this.basePath;
    this.httpClient = configuration?.httpClient || httpClient;
  }

  /**
   * @param consumes string[] mime-types
   * @return true: consumes contains 'multipart/form-data', false: otherwise
   */
  private canConsumeForm(consumes: string[]): boolean {
    const form = 'multipart/form-data';
    return consumes.includes(form);
  }

  /**
   * create a new user
   * Creates a new user account
   * @param createUserPayload
   * @param acceptLanguage Indicate the preferred language for the response (see https://www.rfc-editor.org/rfc/rfc9110#section-12.5.4)
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public accountClientControllerCreateNewUser(
    createUserPayload: CreateUserPayload,
    acceptLanguage?: string
  ): Observable<AxiosResponse<CreateUserResponseDto>>;
  public accountClientControllerCreateNewUser(
    createUserPayload: CreateUserPayload,
    acceptLanguage?: string
  ): Observable<any> {
    if (createUserPayload === null || createUserPayload === undefined) {
      throw new Error(
        'Required parameter createUserPayload was null or undefined when calling accountClientControllerCreateNewUser.'
      );
    }

    const headers = { ...this.defaultHeaders };
    if (acceptLanguage !== undefined && acceptLanguage !== null) {
      headers['Accept-Language'] = String(acceptLanguage);
    }

    const accessTokenObservable: Observable<any> = of(null);

    // to determine the Accept header
    const httpHeaderAccepts: string[] = ['application/json'];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = ['application/json'];
    const httpContentTypeSelected: string | undefined =
      this.configuration.selectHeaderContentType(consumes);
    if (httpContentTypeSelected != undefined) {
      headers['Content-Type'] = httpContentTypeSelected;
    }
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.post<CreateUserResponseDto>(
          `${this.basePath}/auth/users`,
          createUserPayload,
          {
            withCredentials: this.configuration.withCredentials,
            headers: headers,
          }
        );
      })
    );
  }
  /**
   * deletes the user
   * Deletes the user account
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public accountClientControllerDeleteUser(): Observable<AxiosResponse<object>>;
  public accountClientControllerDeleteUser(): Observable<any> {
    const headers = { ...this.defaultHeaders };

    let accessTokenObservable: Observable<any> = of(null);

    // authentication (bearer) required
    if (typeof this.configuration.accessToken === 'function') {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken())
      );
    } else if (this.configuration.accessToken) {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken)
      );
    }
    // to determine the Accept header
    const httpHeaderAccepts: string[] = ['application/json'];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = [];
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.delete<object>(`${this.basePath}/auth/user`, {
          withCredentials: this.configuration.withCredentials,
          headers: headers,
        });
      })
    );
  }
  /**
   * retrieve available telephone codes
   * Retrieves available telephone codes specified by the Firebase configuration
   * @param acceptLanguage Indicate the preferred language for the response (see https://www.rfc-editor.org/rfc/rfc9110#section-12.5.4)
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public accountClientControllerGetTelephoneCodes(
    acceptLanguage?: string
  ): Observable<AxiosResponse<any>>;
  public accountClientControllerGetTelephoneCodes(
    acceptLanguage?: string
  ): Observable<any> {
    const headers = { ...this.defaultHeaders };
    if (acceptLanguage !== undefined && acceptLanguage !== null) {
      headers['Accept-Language'] = String(acceptLanguage);
    }

    const accessTokenObservable: Observable<any> = of(null);

    // to determine the Accept header
    const httpHeaderAccepts: string[] = [];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = [];
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.get<any>(
          `${this.basePath}/auth/telephone-codes`,
          {
            withCredentials: this.configuration.withCredentials,
            headers: headers,
          }
        );
      })
    );
  }
  /**
   * Recover 2 factor autentication for user
   * Sends an email to the user to recover their 2 factor authentication
   * @param sendRecoverFactorRequest
   * @param acceptLanguage Indicate the preferred language for the response (see https://www.rfc-editor.org/rfc/rfc9110#section-12.5.4)
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public accountClientControllerRecoverFactor(
    sendRecoverFactorRequest: SendRecoverFactorRequest,
    acceptLanguage?: string
  ): Observable<AxiosResponse<any>>;
  public accountClientControllerRecoverFactor(
    sendRecoverFactorRequest: SendRecoverFactorRequest,
    acceptLanguage?: string
  ): Observable<any> {
    if (
      sendRecoverFactorRequest === null ||
      sendRecoverFactorRequest === undefined
    ) {
      throw new Error(
        'Required parameter sendRecoverFactorRequest was null or undefined when calling accountClientControllerRecoverFactor.'
      );
    }

    const headers = { ...this.defaultHeaders };
    if (acceptLanguage !== undefined && acceptLanguage !== null) {
      headers['Accept-Language'] = String(acceptLanguage);
    }

    const accessTokenObservable: Observable<any> = of(null);

    // to determine the Accept header
    const httpHeaderAccepts: string[] = [];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = ['application/json'];
    const httpContentTypeSelected: string | undefined =
      this.configuration.selectHeaderContentType(consumes);
    if (httpContentTypeSelected != undefined) {
      headers['Content-Type'] = httpContentTypeSelected;
    }
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.post<any>(
          `${this.basePath}/auth/recover-factor`,
          sendRecoverFactorRequest,
          {
            withCredentials: this.configuration.withCredentials,
            headers: headers,
          }
        );
      })
    );
  }
  /**
   * verify a user\&#39;s email address
   * Sends an email verification email to the user\&#39;s email address
   * @param acceptLanguage Indicate the preferred language for the response (see https://www.rfc-editor.org/rfc/rfc9110#section-12.5.4)
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public accountClientControllerSendEmailVerificationRequest(
    acceptLanguage?: string
  ): Observable<AxiosResponse<any>>;
  public accountClientControllerSendEmailVerificationRequest(
    acceptLanguage?: string
  ): Observable<any> {
    const headers = { ...this.defaultHeaders };
    if (acceptLanguage !== undefined && acceptLanguage !== null) {
      headers['Accept-Language'] = String(acceptLanguage);
    }

    let accessTokenObservable: Observable<any> = of(null);

    // authentication (bearer) required
    if (typeof this.configuration.accessToken === 'function') {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken())
      );
    } else if (this.configuration.accessToken) {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken)
      );
    }
    // to determine the Accept header
    const httpHeaderAccepts: string[] = [];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = [];
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.post<any>(
          `${this.basePath}/auth/email-verification`,
          null,
          {
            withCredentials: this.configuration.withCredentials,
            headers: headers,
          }
        );
      })
    );
  }
  /**
   * reset a user\&#39;s password
   * Sends a reset password email to the user\&#39;s email address
   * @param resetPasswordRequestDto
   * @param acceptLanguage Indicate the preferred language for the response (see https://www.rfc-editor.org/rfc/rfc9110#section-12.5.4)
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public accountClientControllerSendPasswordResetRequest(
    resetPasswordRequestDto: ResetPasswordRequestDto,
    acceptLanguage?: string
  ): Observable<AxiosResponse<any>>;
  public accountClientControllerSendPasswordResetRequest(
    resetPasswordRequestDto: ResetPasswordRequestDto,
    acceptLanguage?: string
  ): Observable<any> {
    if (
      resetPasswordRequestDto === null ||
      resetPasswordRequestDto === undefined
    ) {
      throw new Error(
        'Required parameter resetPasswordRequestDto was null or undefined when calling accountClientControllerSendPasswordResetRequest.'
      );
    }

    const headers = { ...this.defaultHeaders };
    if (acceptLanguage !== undefined && acceptLanguage !== null) {
      headers['Accept-Language'] = String(acceptLanguage);
    }

    const accessTokenObservable: Observable<any> = of(null);

    // to determine the Accept header
    const httpHeaderAccepts: string[] = [];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = ['application/json'];
    const httpContentTypeSelected: string | undefined =
      this.configuration.selectHeaderContentType(consumes);
    if (httpContentTypeSelected != undefined) {
      headers['Content-Type'] = httpContentTypeSelected;
    }
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.post<any>(
          `${this.basePath}/auth/password-reset`,
          resetPasswordRequestDto,
          {
            withCredentials: this.configuration.withCredentials,
            headers: headers,
          }
        );
      })
    );
  }
  /**
   * changes a user\&#39;s email address
   * Sends a verification email to the user\&#39;s new email address, updating the user\&#39;s email address once the new email address has been verified
   * @param sendVerifyAndChangeEmailRequest
   * @param acceptLanguage Indicate the preferred language for the response (see https://www.rfc-editor.org/rfc/rfc9110#section-12.5.4)
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public accountClientControllerSendVerifyAndChangeEmail(
    sendVerifyAndChangeEmailRequest: SendVerifyAndChangeEmailRequest,
    acceptLanguage?: string
  ): Observable<AxiosResponse<any>>;
  public accountClientControllerSendVerifyAndChangeEmail(
    sendVerifyAndChangeEmailRequest: SendVerifyAndChangeEmailRequest,
    acceptLanguage?: string
  ): Observable<any> {
    if (
      sendVerifyAndChangeEmailRequest === null ||
      sendVerifyAndChangeEmailRequest === undefined
    ) {
      throw new Error(
        'Required parameter sendVerifyAndChangeEmailRequest was null or undefined when calling accountClientControllerSendVerifyAndChangeEmail.'
      );
    }

    const headers = { ...this.defaultHeaders };
    if (acceptLanguage !== undefined && acceptLanguage !== null) {
      headers['Accept-Language'] = String(acceptLanguage);
    }

    let accessTokenObservable: Observable<any> = of(null);

    // authentication (bearer) required
    if (typeof this.configuration.accessToken === 'function') {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken())
      );
    } else if (this.configuration.accessToken) {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken)
      );
    }
    // to determine the Accept header
    const httpHeaderAccepts: string[] = [];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = ['application/json'];
    const httpContentTypeSelected: string | undefined =
      this.configuration.selectHeaderContentType(consumes);
    if (httpContentTypeSelected != undefined) {
      headers['Content-Type'] = httpContentTypeSelected;
    }
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.post<any>(
          `${this.basePath}/auth/verify-and-change-email`,
          sendVerifyAndChangeEmailRequest,
          {
            withCredentials: this.configuration.withCredentials,
            headers: headers,
          }
        );
      })
    );
  }
  /**
   * Proxy update user
   * proxy call the call to driver account api
   * @param updateUserDto
   * @param acceptLanguage Indicate the preferred language for the response (see https://www.rfc-editor.org/rfc/rfc9110#section-12.5.4)
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public accountClientControllerUpdateUser(
    updateUserDto: UpdateUserDto,
    acceptLanguage?: string
  ): Observable<AxiosResponse<UserDetailsDto>>;
  public accountClientControllerUpdateUser(
    updateUserDto: UpdateUserDto,
    acceptLanguage?: string
  ): Observable<any> {
    if (updateUserDto === null || updateUserDto === undefined) {
      throw new Error(
        'Required parameter updateUserDto was null or undefined when calling accountClientControllerUpdateUser.'
      );
    }

    const headers = { ...this.defaultHeaders };
    if (acceptLanguage !== undefined && acceptLanguage !== null) {
      headers['Accept-Language'] = String(acceptLanguage);
    }

    let accessTokenObservable: Observable<any> = of(null);

    // authentication (bearer) required
    if (typeof this.configuration.accessToken === 'function') {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken())
      );
    } else if (this.configuration.accessToken) {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken)
      );
    }
    // to determine the Accept header
    const httpHeaderAccepts: string[] = ['application/json'];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = ['application/json'];
    const httpContentTypeSelected: string | undefined =
      this.configuration.selectHeaderContentType(consumes);
    if (httpContentTypeSelected != undefined) {
      headers['Content-Type'] = httpContentTypeSelected;
    }
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.put<UserDetailsDto>(
          `${this.basePath}/auth/user`,
          updateUserDto,
          {
            withCredentials: this.configuration.withCredentials,
            headers: headers,
          }
        );
      })
    );
  }
}
