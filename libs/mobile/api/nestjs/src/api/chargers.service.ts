/**
 * Mobile API
 * Mobile Backend for Frontend API service
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
/* tslint:disable:no-unused-variable member-ordering */

import { Injectable, Optional } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { AxiosResponse } from 'axios';
import { Observable, from, of, switchMap } from 'rxjs';
import { ChargeOverrideRequestDTO } from '../model/chargeOverrideRequestDTO';
import { ChargeOverrideScheduleResponse } from '../model/chargeOverrideScheduleResponse';
import { ChargerConnectivityStatus } from '../model/chargerConnectivityStatus';
import { ChargerFlexEnrolment } from '../model/chargerFlexEnrolment';
import { ChargerModelInfoResponse } from '../model/chargerModelInfoResponse';
import { ChargerResponse } from '../model/chargerResponse';
import { ChargerTariffDto } from '../model/chargerTariffDto';
import { ChargingStationTariffSearchDto } from '../model/chargingStationTariffSearchDto';
import { FirmwareStatusResponse } from '../model/firmwareStatusResponse';
import { FlexRequest } from '../model/flexRequest';
import { Region } from '../model/region';
import { Restrictions } from '../model/restrictions';
import { SetChargerTariffDto } from '../model/setChargerTariffDto';
import { SolarPreferences } from '../model/solarPreferences';
import { WifiCredentials } from '../model/wifiCredentials';
import { Configuration } from '../configuration';

@Injectable()
export class ChargersService {
  protected basePath = 'http://localhost';
  public defaultHeaders: Record<string, string> = {};
  public configuration = new Configuration();
  protected httpClient: HttpService;

  constructor(
    httpClient: HttpService,
    @Optional() configuration: Configuration
  ) {
    this.configuration = configuration || this.configuration;
    this.basePath = configuration?.basePath || this.basePath;
    this.httpClient = configuration?.httpClient || httpClient;
  }

  /**
   * @param consumes string[] mime-types
   * @return true: consumes contains 'multipart/form-data', false: otherwise
   */
  private canConsumeForm(consumes: string[]): boolean {
    const form = 'multipart/form-data';
    return consumes.includes(form);
  }

  /**
   * create charge overrides for a given charger
   * For a given PPID, create a charge override
   * @param ppid PPID of a given charger
   * @param chargeOverrideRequestDTO
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public chargersControllerCreateChargerOverrides(
    ppid: string,
    chargeOverrideRequestDTO: ChargeOverrideRequestDTO
  ): Observable<AxiosResponse<ChargeOverrideScheduleResponse[]>>;
  public chargersControllerCreateChargerOverrides(
    ppid: string,
    chargeOverrideRequestDTO: ChargeOverrideRequestDTO
  ): Observable<any> {
    if (ppid === null || ppid === undefined) {
      throw new Error(
        'Required parameter ppid was null or undefined when calling chargersControllerCreateChargerOverrides.'
      );
    }

    if (
      chargeOverrideRequestDTO === null ||
      chargeOverrideRequestDTO === undefined
    ) {
      throw new Error(
        'Required parameter chargeOverrideRequestDTO was null or undefined when calling chargersControllerCreateChargerOverrides.'
      );
    }

    const headers = { ...this.defaultHeaders };

    let accessTokenObservable: Observable<any> = of(null);

    // authentication (bearer) required
    if (typeof this.configuration.accessToken === 'function') {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken())
      );
    } else if (this.configuration.accessToken) {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken)
      );
    }
    // to determine the Accept header
    const httpHeaderAccepts: string[] = ['application/json'];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = ['application/json'];
    const httpContentTypeSelected: string | undefined =
      this.configuration.selectHeaderContentType(consumes);
    if (httpContentTypeSelected != undefined) {
      headers['Content-Type'] = httpContentTypeSelected;
    }
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.post<ChargeOverrideScheduleResponse[]>(
          `${this.basePath}/chargers/${encodeURIComponent(
            String(ppid)
          )}/charge-overrides`,
          chargeOverrideRequestDTO,
          {
            withCredentials: this.configuration.withCredentials,
            headers: headers,
          }
        );
      })
    );
  }
  /**
   * delete charge overrides for a given charger
   * For a given PPID, delete the charge override
   * @param ppid PPID of a given charger
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public chargersControllerDeleteChargeOverride(
    ppid: string
  ): Observable<AxiosResponse<any>>;
  public chargersControllerDeleteChargeOverride(ppid: string): Observable<any> {
    if (ppid === null || ppid === undefined) {
      throw new Error(
        'Required parameter ppid was null or undefined when calling chargersControllerDeleteChargeOverride.'
      );
    }

    const headers = { ...this.defaultHeaders };

    let accessTokenObservable: Observable<any> = of(null);

    // authentication (bearer) required
    if (typeof this.configuration.accessToken === 'function') {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken())
      );
    } else if (this.configuration.accessToken) {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken)
      );
    }
    // to determine the Accept header
    const httpHeaderAccepts: string[] = [];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = [];
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.delete<any>(
          `${this.basePath}/chargers/${encodeURIComponent(
            String(ppid)
          )}/charge-overrides`,
          {
            withCredentials: this.configuration.withCredentials,
            headers: headers,
          }
        );
      })
    );
  }
  /**
   * delete a charger\&#39;s flex enrolment
   * For a given PPID and authenticated user, delete the charger\&#39;s flex enrolment
   * @param ppid PPID of a given charger
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public chargersControllerDeleteFlexEnrolment(
    ppid: string
  ): Observable<AxiosResponse<any>>;
  public chargersControllerDeleteFlexEnrolment(ppid: string): Observable<any> {
    if (ppid === null || ppid === undefined) {
      throw new Error(
        'Required parameter ppid was null or undefined when calling chargersControllerDeleteFlexEnrolment.'
      );
    }

    const headers = { ...this.defaultHeaders };

    let accessTokenObservable: Observable<any> = of(null);

    // authentication (bearer) required
    if (typeof this.configuration.accessToken === 'function') {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken())
      );
    } else if (this.configuration.accessToken) {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken)
      );
    }
    // to determine the Accept header
    const httpHeaderAccepts: string[] = ['application/json'];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = [];
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.delete<any>(
          `${this.basePath}/chargers/${encodeURIComponent(
            String(ppid)
          )}/flex-enrolment`,
          {
            withCredentials: this.configuration.withCredentials,
            headers: headers,
          }
        );
      })
    );
  }
  /**
   * delete a flex request
   * Delete a flex request for the given charger
   * @param id ID of the flex event to opt out of
   * @param ppid PPID of a given charger
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public chargersControllerDeleteFlexRequest(
    id: string,
    ppid: any
  ): Observable<AxiosResponse<any>>;
  public chargersControllerDeleteFlexRequest(
    id: string,
    ppid: any
  ): Observable<any> {
    if (id === null || id === undefined) {
      throw new Error(
        'Required parameter id was null or undefined when calling chargersControllerDeleteFlexRequest.'
      );
    }

    if (ppid === null || ppid === undefined) {
      throw new Error(
        'Required parameter ppid was null or undefined when calling chargersControllerDeleteFlexRequest.'
      );
    }

    const headers = { ...this.defaultHeaders };

    let accessTokenObservable: Observable<any> = of(null);

    // authentication (bearer) required
    if (typeof this.configuration.accessToken === 'function') {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken())
      );
    } else if (this.configuration.accessToken) {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken)
      );
    }
    // to determine the Accept header
    const httpHeaderAccepts: string[] = ['application/json'];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = [];
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.delete<any>(
          `${this.basePath}/chargers/${encodeURIComponent(
            String(ppid)
          )}/flex-requests/${encodeURIComponent(String(id))}`,
          {
            withCredentials: this.configuration.withCredentials,
            headers: headers,
          }
        );
      })
    );
  }
  /**
   * Delete tariff for a given charger
   * For a given PPID and TariffId, delete the charger\&#39;s tariff
   * @param ppid PPID of a given charger
   * @param tariffId Id of a given tariff
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public chargersControllerDeleteTariff(
    ppid: string,
    tariffId: string
  ): Observable<AxiosResponse<any>>;
  public chargersControllerDeleteTariff(
    ppid: string,
    tariffId: string
  ): Observable<any> {
    if (ppid === null || ppid === undefined) {
      throw new Error(
        'Required parameter ppid was null or undefined when calling chargersControllerDeleteTariff.'
      );
    }

    if (tariffId === null || tariffId === undefined) {
      throw new Error(
        'Required parameter tariffId was null or undefined when calling chargersControllerDeleteTariff.'
      );
    }

    const headers = { ...this.defaultHeaders };

    let accessTokenObservable: Observable<any> = of(null);

    // authentication (bearer) required
    if (typeof this.configuration.accessToken === 'function') {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken())
      );
    } else if (this.configuration.accessToken) {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken)
      );
    }
    // to determine the Accept header
    const httpHeaderAccepts: string[] = ['application/json'];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = [];
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.delete<any>(
          `${this.basePath}/chargers/${encodeURIComponent(
            String(ppid)
          )}/tariffs/${encodeURIComponent(String(tariffId))}`,
          {
            withCredentials: this.configuration.withCredentials,
            headers: headers,
          }
        );
      })
    );
  }
  /**
   * retrieve firmware for a given charger
   * For a given PPID, retrieve the firmware
   * @param ppid
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public chargersControllerGetChargerFirmware(
    ppid: string
  ): Observable<AxiosResponse<FirmwareStatusResponse>>;
  public chargersControllerGetChargerFirmware(ppid: string): Observable<any> {
    if (ppid === null || ppid === undefined) {
      throw new Error(
        'Required parameter ppid was null or undefined when calling chargersControllerGetChargerFirmware.'
      );
    }

    const headers = { ...this.defaultHeaders };

    let accessTokenObservable: Observable<any> = of(null);

    // authentication (bearer) required
    if (typeof this.configuration.accessToken === 'function') {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken())
      );
    } else if (this.configuration.accessToken) {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken)
      );
    }
    // to determine the Accept header
    const httpHeaderAccepts: string[] = ['application/json'];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = [];
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.get<FirmwareStatusResponse>(
          `${this.basePath}/chargers/${encodeURIComponent(
            String(ppid)
          )}/firmware`,
          {
            withCredentials: this.configuration.withCredentials,
            headers: headers,
          }
        );
      })
    );
  }
  /**
   * get model information for a given charger
   * For a given PPID, get information about the charger
   * @param ppid PPID of a given charger
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public chargersControllerGetChargerModelInfo(
    ppid: string
  ): Observable<AxiosResponse<ChargerModelInfoResponse>>;
  public chargersControllerGetChargerModelInfo(ppid: string): Observable<any> {
    if (ppid === null || ppid === undefined) {
      throw new Error(
        'Required parameter ppid was null or undefined when calling chargersControllerGetChargerModelInfo.'
      );
    }

    const headers = { ...this.defaultHeaders };

    let accessTokenObservable: Observable<any> = of(null);

    // authentication (bearer) required
    if (typeof this.configuration.accessToken === 'function') {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken())
      );
    } else if (this.configuration.accessToken) {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken)
      );
    }
    // to determine the Accept header
    const httpHeaderAccepts: string[] = ['application/json'];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = [];
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.get<ChargerModelInfoResponse>(
          `${this.basePath}/chargers/${encodeURIComponent(
            String(ppid)
          )}/model-info`,
          {
            withCredentials: this.configuration.withCredentials,
            headers: headers,
          }
        );
      })
    );
  }
  /**
   * get charge overrides for a given charger
   * For a given PPID, get the charge override
   * @param ppid PPID of a given charger
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public chargersControllerGetChargerOverrides(
    ppid: string
  ): Observable<AxiosResponse<ChargeOverrideScheduleResponse[]>>;
  public chargersControllerGetChargerOverrides(ppid: string): Observable<any> {
    if (ppid === null || ppid === undefined) {
      throw new Error(
        'Required parameter ppid was null or undefined when calling chargersControllerGetChargerOverrides.'
      );
    }

    const headers = { ...this.defaultHeaders };

    let accessTokenObservable: Observable<any> = of(null);

    // authentication (bearer) required
    if (typeof this.configuration.accessToken === 'function') {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken())
      );
    } else if (this.configuration.accessToken) {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken)
      );
    }
    // to determine the Accept header
    const httpHeaderAccepts: string[] = ['application/json'];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = [];
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.get<ChargeOverrideScheduleResponse[]>(
          `${this.basePath}/chargers/${encodeURIComponent(
            String(ppid)
          )}/charge-overrides`,
          {
            withCredentials: this.configuration.withCredentials,
            headers: headers,
          }
        );
      })
    );
  }
  /**
   * retrieve tariff for a given charger
   * For a given PPID, retrieve the charger
   * @param ppid PPID of a given charger
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public chargersControllerGetChargerTariffs(
    ppid: string
  ): Observable<AxiosResponse<ChargingStationTariffSearchDto>>;
  public chargersControllerGetChargerTariffs(ppid: string): Observable<any> {
    if (ppid === null || ppid === undefined) {
      throw new Error(
        'Required parameter ppid was null or undefined when calling chargersControllerGetChargerTariffs.'
      );
    }

    const headers = { ...this.defaultHeaders };

    let accessTokenObservable: Observable<any> = of(null);

    // authentication (bearer) required
    if (typeof this.configuration.accessToken === 'function') {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken())
      );
    } else if (this.configuration.accessToken) {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken)
      );
    }
    // to determine the Accept header
    const httpHeaderAccepts: string[] = ['application/json'];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = [];
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.get<ChargingStationTariffSearchDto>(
          `${this.basePath}/chargers/${encodeURIComponent(
            String(ppid)
          )}/tariffs`,
          {
            withCredentials: this.configuration.withCredentials,
            headers: headers,
          }
        );
      })
    );
  }
  /**
   * retrieve all chargers
   * For the authenticated user, return all their chargers
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public chargersControllerGetChargers(): Observable<
    AxiosResponse<ChargerResponse[]>
  >;
  public chargersControllerGetChargers(): Observable<any> {
    const headers = { ...this.defaultHeaders };

    let accessTokenObservable: Observable<any> = of(null);

    // authentication (bearer) required
    if (typeof this.configuration.accessToken === 'function') {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken())
      );
    } else if (this.configuration.accessToken) {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken)
      );
    }
    // to determine the Accept header
    const httpHeaderAccepts: string[] = ['application/json'];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = [];
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.get<ChargerResponse[]>(
          `${this.basePath}/chargers`,
          {
            withCredentials: this.configuration.withCredentials,
            headers: headers,
          }
        );
      })
    );
  }
  /**
   * retrieve charger region
   * For a given PSL / PPID, return the associated DNO region id.
   * @param ppid PPID of a given charger
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public chargersControllerGetDnoRegion(
    ppid: string
  ): Observable<AxiosResponse<Region>>;
  public chargersControllerGetDnoRegion(ppid: string): Observable<any> {
    if (ppid === null || ppid === undefined) {
      throw new Error(
        'Required parameter ppid was null or undefined when calling chargersControllerGetDnoRegion.'
      );
    }

    const headers = { ...this.defaultHeaders };

    let accessTokenObservable: Observable<any> = of(null);

    // authentication (bearer) required
    if (typeof this.configuration.accessToken === 'function') {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken())
      );
    } else if (this.configuration.accessToken) {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken)
      );
    }
    // to determine the Accept header
    const httpHeaderAccepts: string[] = ['application/json'];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = [];
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.get<Region>(
          `${this.basePath}/chargers/${encodeURIComponent(
            String(ppid)
          )}/dnoregion`,
          {
            withCredentials: this.configuration.withCredentials,
            headers: headers,
          }
        );
      })
    );
  }
  /**
   * retrieve charger\&#39;s flex enrolment
   * For a given PPID and authenticated user, return the charger\&#39;s flex enrolment
   * @param ppid PPID of a given charger
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public chargersControllerGetFlexEnrolment(
    ppid: string
  ): Observable<AxiosResponse<ChargerFlexEnrolment>>;
  public chargersControllerGetFlexEnrolment(ppid: string): Observable<any> {
    if (ppid === null || ppid === undefined) {
      throw new Error(
        'Required parameter ppid was null or undefined when calling chargersControllerGetFlexEnrolment.'
      );
    }

    const headers = { ...this.defaultHeaders };

    let accessTokenObservable: Observable<any> = of(null);

    // authentication (bearer) required
    if (typeof this.configuration.accessToken === 'function') {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken())
      );
    } else if (this.configuration.accessToken) {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken)
      );
    }
    // to determine the Accept header
    const httpHeaderAccepts: string[] = ['application/json'];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = [];
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.get<ChargerFlexEnrolment>(
          `${this.basePath}/chargers/${encodeURIComponent(
            String(ppid)
          )}/flex-enrolment`,
          {
            withCredentials: this.configuration.withCredentials,
            headers: headers,
          }
        );
      })
    );
  }
  /**
   * retrieve a list of active flex requests for this charger
   * For a given PPID and authenticated user, return the charger\&#39;s active flex requests
   * @param ppid PPID of a given charger
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public chargersControllerGetFlexRequests(
    ppid: string
  ): Observable<AxiosResponse<FlexRequest[]>>;
  public chargersControllerGetFlexRequests(ppid: string): Observable<any> {
    if (ppid === null || ppid === undefined) {
      throw new Error(
        'Required parameter ppid was null or undefined when calling chargersControllerGetFlexRequests.'
      );
    }

    const headers = { ...this.defaultHeaders };

    let accessTokenObservable: Observable<any> = of(null);

    // authentication (bearer) required
    if (typeof this.configuration.accessToken === 'function') {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken())
      );
    } else if (this.configuration.accessToken) {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken)
      );
    }
    // to determine the Accept header
    const httpHeaderAccepts: string[] = ['application/json'];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = [];
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.get<FlexRequest[]>(
          `${this.basePath}/chargers/${encodeURIComponent(
            String(ppid)
          )}/flex-requests`,
          {
            withCredentials: this.configuration.withCredentials,
            headers: headers,
          }
        );
      })
    );
  }
  /**
   * retrieve charger\&#39;s allowance based on authenticated user
   * For a given PSL / PPID and authenticated user, return the charger\&#39;s usage allowance
   * @param ppid PPID of a given charger
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public chargersControllerGetRestrictions(
    ppid: string
  ): Observable<AxiosResponse<Restrictions>>;
  public chargersControllerGetRestrictions(ppid: string): Observable<any> {
    if (ppid === null || ppid === undefined) {
      throw new Error(
        'Required parameter ppid was null or undefined when calling chargersControllerGetRestrictions.'
      );
    }

    const headers = { ...this.defaultHeaders };

    let accessTokenObservable: Observable<any> = of(null);

    // authentication (bearer) required
    if (typeof this.configuration.accessToken === 'function') {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken())
      );
    } else if (this.configuration.accessToken) {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken)
      );
    }
    // to determine the Accept header
    const httpHeaderAccepts: string[] = ['application/json'];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = [];
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.get<Restrictions>(
          `${this.basePath}/chargers/${encodeURIComponent(
            String(ppid)
          )}/restrictions`,
          {
            withCredentials: this.configuration.withCredentials,
            headers: headers,
          }
        );
      })
    );
  }
  /**
   * retrieve solar preferences for this charger
   * For a given PPID and authenticated user, return the charger\&#39;s solar preferences
   * @param ppid PPID of a given charger
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public chargersControllerGetSolarPreferences(
    ppid: string
  ): Observable<AxiosResponse<SolarPreferences>>;
  public chargersControllerGetSolarPreferences(ppid: string): Observable<any> {
    if (ppid === null || ppid === undefined) {
      throw new Error(
        'Required parameter ppid was null or undefined when calling chargersControllerGetSolarPreferences.'
      );
    }

    const headers = { ...this.defaultHeaders };

    let accessTokenObservable: Observable<any> = of(null);

    // authentication (bearer) required
    if (typeof this.configuration.accessToken === 'function') {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken())
      );
    } else if (this.configuration.accessToken) {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken)
      );
    }
    // to determine the Accept header
    const httpHeaderAccepts: string[] = ['application/json'];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = [];
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.get<SolarPreferences>(
          `${this.basePath}/chargers/${encodeURIComponent(
            String(ppid)
          )}/solar/preferences`,
          {
            withCredentials: this.configuration.withCredentials,
            headers: headers,
          }
        );
      })
    );
  }
  /**
   * retrieve charger\&#39;s connectivity status and energy offer statuses based on authenticated user
   * For a given PPID and authenticated user, return the charger\&#39;s connectivity status and energy offer statuses
   * @param ppid PPID of a given charger
   * @param userAgent
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public chargersControllerGetStatus(
    ppid: string,
    userAgent?: string
  ): Observable<AxiosResponse<ChargerConnectivityStatus>>;
  public chargersControllerGetStatus(
    ppid: string,
    userAgent?: string
  ): Observable<any> {
    if (ppid === null || ppid === undefined) {
      throw new Error(
        'Required parameter ppid was null or undefined when calling chargersControllerGetStatus.'
      );
    }

    const headers = { ...this.defaultHeaders };
    if (userAgent !== undefined && userAgent !== null) {
      headers['user-agent'] = String(userAgent);
    }

    let accessTokenObservable: Observable<any> = of(null);

    // authentication (bearer) required
    if (typeof this.configuration.accessToken === 'function') {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken())
      );
    } else if (this.configuration.accessToken) {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken)
      );
    }
    // to determine the Accept header
    const httpHeaderAccepts: string[] = ['application/json'];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = [];
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.get<ChargerConnectivityStatus>(
          `${this.basePath}/chargers/${encodeURIComponent(
            String(ppid)
          )}/connectivity-status`,
          {
            withCredentials: this.configuration.withCredentials,
            headers: headers,
          }
        );
      })
    );
  }
  /**
   * set tariff for a given charger
   * For a given PPID, set the charger\&#39;s tariff
   * @param ppid PPID of a given charger
   * @param setChargerTariffDto
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public chargersControllerSetChargerTariffs(
    ppid: string,
    setChargerTariffDto: SetChargerTariffDto
  ): Observable<AxiosResponse<ChargerTariffDto>>;
  public chargersControllerSetChargerTariffs(
    ppid: string,
    setChargerTariffDto: SetChargerTariffDto
  ): Observable<any> {
    if (ppid === null || ppid === undefined) {
      throw new Error(
        'Required parameter ppid was null or undefined when calling chargersControllerSetChargerTariffs.'
      );
    }

    if (setChargerTariffDto === null || setChargerTariffDto === undefined) {
      throw new Error(
        'Required parameter setChargerTariffDto was null or undefined when calling chargersControllerSetChargerTariffs.'
      );
    }

    const headers = { ...this.defaultHeaders };

    let accessTokenObservable: Observable<any> = of(null);

    // authentication (bearer) required
    if (typeof this.configuration.accessToken === 'function') {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken())
      );
    } else if (this.configuration.accessToken) {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken)
      );
    }
    // to determine the Accept header
    const httpHeaderAccepts: string[] = ['application/json'];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = ['application/json'];
    const httpContentTypeSelected: string | undefined =
      this.configuration.selectHeaderContentType(consumes);
    if (httpContentTypeSelected != undefined) {
      headers['Content-Type'] = httpContentTypeSelected;
    }
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.post<ChargerTariffDto>(
          `${this.basePath}/chargers/${encodeURIComponent(
            String(ppid)
          )}/tariffs`,
          setChargerTariffDto,
          {
            withCredentials: this.configuration.withCredentials,
            headers: headers,
          }
        );
      })
    );
  }
  /**
   * sets solar preferences for this charger
   * For a given PPID and authenticated user, sets the charger\&#39;s solar preferences
   * @param ppid PPID of a given charger
   * @param solarPreferences
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public chargersControllerSetSolarPreferences(
    ppid: string,
    solarPreferences: SolarPreferences
  ): Observable<AxiosResponse<SolarPreferences>>;
  public chargersControllerSetSolarPreferences(
    ppid: string,
    solarPreferences: SolarPreferences
  ): Observable<any> {
    if (ppid === null || ppid === undefined) {
      throw new Error(
        'Required parameter ppid was null or undefined when calling chargersControllerSetSolarPreferences.'
      );
    }

    if (solarPreferences === null || solarPreferences === undefined) {
      throw new Error(
        'Required parameter solarPreferences was null or undefined when calling chargersControllerSetSolarPreferences.'
      );
    }

    const headers = { ...this.defaultHeaders };

    let accessTokenObservable: Observable<any> = of(null);

    // authentication (bearer) required
    if (typeof this.configuration.accessToken === 'function') {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken())
      );
    } else if (this.configuration.accessToken) {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken)
      );
    }
    // to determine the Accept header
    const httpHeaderAccepts: string[] = ['application/json'];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = ['application/json'];
    const httpContentTypeSelected: string | undefined =
      this.configuration.selectHeaderContentType(consumes);
    if (httpContentTypeSelected != undefined) {
      headers['Content-Type'] = httpContentTypeSelected;
    }
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.post<SolarPreferences>(
          `${this.basePath}/chargers/${encodeURIComponent(
            String(ppid)
          )}/solar/preferences`,
          solarPreferences,
          {
            withCredentials: this.configuration.withCredentials,
            headers: headers,
          }
        );
      })
    );
  }
  /**
   *
   * Given a PPID and tariff ID, replace the tariff details with the provided details
   * @param ppid PPID of a given charger
   * @param tariffId Id of a given tariff
   * @param setChargerTariffDto
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public chargersControllerUpdateChargerTariff(
    ppid: string,
    tariffId: string,
    setChargerTariffDto: SetChargerTariffDto
  ): Observable<AxiosResponse<ChargerTariffDto>>;
  public chargersControllerUpdateChargerTariff(
    ppid: string,
    tariffId: string,
    setChargerTariffDto: SetChargerTariffDto
  ): Observable<any> {
    if (ppid === null || ppid === undefined) {
      throw new Error(
        'Required parameter ppid was null or undefined when calling chargersControllerUpdateChargerTariff.'
      );
    }

    if (tariffId === null || tariffId === undefined) {
      throw new Error(
        'Required parameter tariffId was null or undefined when calling chargersControllerUpdateChargerTariff.'
      );
    }

    if (setChargerTariffDto === null || setChargerTariffDto === undefined) {
      throw new Error(
        'Required parameter setChargerTariffDto was null or undefined when calling chargersControllerUpdateChargerTariff.'
      );
    }

    const headers = { ...this.defaultHeaders };

    let accessTokenObservable: Observable<any> = of(null);

    // authentication (bearer) required
    if (typeof this.configuration.accessToken === 'function') {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken())
      );
    } else if (this.configuration.accessToken) {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken)
      );
    }
    // to determine the Accept header
    const httpHeaderAccepts: string[] = ['application/json'];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = ['application/json'];
    const httpContentTypeSelected: string | undefined =
      this.configuration.selectHeaderContentType(consumes);
    if (httpContentTypeSelected != undefined) {
      headers['Content-Type'] = httpContentTypeSelected;
    }
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.put<ChargerTariffDto>(
          `${this.basePath}/chargers/${encodeURIComponent(
            String(ppid)
          )}/tariffs/${encodeURIComponent(String(tariffId))}`,
          setChargerTariffDto,
          {
            withCredentials: this.configuration.withCredentials,
            headers: headers,
          }
        );
      })
    );
  }
  /**
   * retrieve wifi credentials of arch5 charger
   * For a given PSL / PPID of an arch5 charger, return the wifi credentials.
   * @param ppid
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public ssidPasswordControllerGetWifiCredentials(
    ppid: string
  ): Observable<AxiosResponse<WifiCredentials>>;
  public ssidPasswordControllerGetWifiCredentials(
    ppid: string
  ): Observable<any> {
    if (ppid === null || ppid === undefined) {
      throw new Error(
        'Required parameter ppid was null or undefined when calling ssidPasswordControllerGetWifiCredentials.'
      );
    }

    const headers = { ...this.defaultHeaders };

    let accessTokenObservable: Observable<any> = of(null);

    // authentication (bearer) required
    if (typeof this.configuration.accessToken === 'function') {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken())
      );
    } else if (this.configuration.accessToken) {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken)
      );
    }
    // to determine the Accept header
    const httpHeaderAccepts: string[] = ['application/json'];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = [];
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.get<WifiCredentials>(
          `${this.basePath}/chargers/arch5/${encodeURIComponent(String(ppid))}`,
          {
            withCredentials: this.configuration.withCredentials,
            headers: headers,
          }
        );
      })
    );
  }
}
