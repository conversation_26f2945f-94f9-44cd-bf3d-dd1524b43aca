/**
 * Mobile API
 * Mobile Backend for Frontend API service
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
/* tslint:disable:no-unused-variable member-ordering */

import { Injectable, Optional } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { AxiosResponse } from 'axios';
import { Observable, from, of, switchMap } from 'rxjs';
import { CreateIntentResponse } from '../model/createIntentResponse';
import { CreatePaymentRequest } from '../model/createPaymentRequest';
import { CreateRegisteredUserPaymentResponse } from '../model/createRegisteredUserPaymentResponse';
import { Configuration } from '../configuration';

@Injectable()
export class PaymentsService {
  protected basePath = 'http://localhost';
  public defaultHeaders: Record<string, string> = {};
  public configuration = new Configuration();
  protected httpClient: HttpService;

  constructor(
    httpClient: HttpService,
    @Optional() configuration: Configuration
  ) {
    this.configuration = configuration || this.configuration;
    this.basePath = configuration?.basePath || this.basePath;
    this.httpClient = configuration?.httpClient || httpClient;
  }

  /**
   * @param consumes string[] mime-types
   * @return true: consumes contains 'multipart/form-data', false: otherwise
   */
  private canConsumeForm(consumes: string[]): boolean {
    const form = 'multipart/form-data';
    return consumes.includes(form);
  }

  /**
   * create setup intent for payment
   * Creates setup intent for payment
   * @param createPaymentRequest
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public paymentControllerCreatePaymentIntent(
    createPaymentRequest: CreatePaymentRequest
  ): Observable<AxiosResponse<CreateRegisteredUserPaymentResponse>>;
  public paymentControllerCreatePaymentIntent(
    createPaymentRequest: CreatePaymentRequest
  ): Observable<any> {
    if (createPaymentRequest === null || createPaymentRequest === undefined) {
      throw new Error(
        'Required parameter createPaymentRequest was null or undefined when calling paymentControllerCreatePaymentIntent.'
      );
    }

    const headers = { ...this.defaultHeaders };

    let accessTokenObservable: Observable<any> = of(null);

    // authentication (bearer) required
    if (typeof this.configuration.accessToken === 'function') {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken())
      );
    } else if (this.configuration.accessToken) {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken)
      );
    }
    // to determine the Accept header
    const httpHeaderAccepts: string[] = ['application/json'];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = ['application/json'];
    const httpContentTypeSelected: string | undefined =
      this.configuration.selectHeaderContentType(consumes);
    if (httpContentTypeSelected != undefined) {
      headers['Content-Type'] = httpContentTypeSelected;
    }
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.post<CreateRegisteredUserPaymentResponse>(
          `${this.basePath}/payments/create-payment-intent`,
          createPaymentRequest,
          {
            withCredentials: this.configuration.withCredentials,
            headers: headers,
          }
        );
      })
    );
  }
  /**
   * create setup intent for payment
   * Creates setup intent for payment
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public paymentControllerSetupIntent(): Observable<
    AxiosResponse<CreateIntentResponse>
  >;
  public paymentControllerSetupIntent(): Observable<any> {
    const headers = { ...this.defaultHeaders };

    const accessTokenObservable: Observable<any> = of(null);

    // to determine the Accept header
    const httpHeaderAccepts: string[] = ['application/json'];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = [];
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.post<CreateIntentResponse>(
          `${this.basePath}/payments/setup-intent`,
          null,
          {
            withCredentials: this.configuration.withCredentials,
            headers: headers,
          }
        );
      })
    );
  }
  /**
   *
   *
   * @param stripeSignature
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public paymentControllerWebhook(
    stripeSignature: string
  ): Observable<AxiosResponse<any>>;
  public paymentControllerWebhook(stripeSignature: string): Observable<any> {
    if (stripeSignature === null || stripeSignature === undefined) {
      throw new Error(
        'Required parameter stripeSignature was null or undefined when calling paymentControllerWebhook.'
      );
    }

    const headers = { ...this.defaultHeaders };
    if (stripeSignature !== undefined && stripeSignature !== null) {
      headers['stripe-signature'] = String(stripeSignature);
    }

    const accessTokenObservable: Observable<any> = of(null);

    // to determine the Accept header
    const httpHeaderAccepts: string[] = [];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = [];
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.post<any>(
          `${this.basePath}/payments/webhook`,
          null,
          {
            withCredentials: this.configuration.withCredentials,
            headers: headers,
          }
        );
      })
    );
  }
}
