/**
 * Mobile API
 * Mobile Backend for Frontend API service
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
/* tslint:disable:no-unused-variable member-ordering */

import { Injectable, Optional } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { AxiosResponse } from 'axios';
import { Observable, from, of, switchMap } from 'rxjs';
import { ChargersAndVehicles } from '../model/chargersAndVehicles';
import { DelegatedControlChargingStationResponseDtoImpl } from '../model/delegatedControlChargingStationResponseDtoImpl';
import { SetVehicleIntent } from '../model/setVehicleIntent';
import { SmartChargingControllerUpdateVehicle200Response } from '../model/smartChargingControllerUpdateVehicle200Response';
import { UpdateVehicleLinkRequestDtoImpl } from '../model/updateVehicleLinkRequestDtoImpl';
import { VehicleIntentsRequestDtoImpl } from '../model/vehicleIntentsRequestDtoImpl';
import { VehicleLinkRequestDtoImpl } from '../model/vehicleLinkRequestDtoImpl';
import { VehicleLinkResponseDtoImpl } from '../model/vehicleLinkResponseDtoImpl';
import { Configuration } from '../configuration';

@Injectable()
export class SmartChargingService {
  protected basePath = 'http://localhost';
  public defaultHeaders: Record<string, string> = {};
  public configuration = new Configuration();
  protected httpClient: HttpService;

  constructor(
    httpClient: HttpService,
    @Optional() configuration: Configuration
  ) {
    this.configuration = configuration || this.configuration;
    this.basePath = configuration?.basePath || this.basePath;
    this.httpClient = configuration?.httpClient || httpClient;
  }

  /**
   * @param consumes string[] mime-types
   * @return true: consumes contains 'multipart/form-data', false: otherwise
   */
  private canConsumeForm(consumes: string[]): boolean {
    const form = 'multipart/form-data';
    return consumes.includes(form);
  }

  /**
   * capture a vehicle for smart charging
   * For a given PPID, capture a vehicle for smart charging
   * @param ppid PPID of a given charger
   * @param vehicleLinkRequestDtoImpl
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public smartChargingControllerCaptureVehicle(
    ppid: string,
    vehicleLinkRequestDtoImpl: VehicleLinkRequestDtoImpl
  ): Observable<AxiosResponse<VehicleLinkResponseDtoImpl>>;
  public smartChargingControllerCaptureVehicle(
    ppid: string,
    vehicleLinkRequestDtoImpl: VehicleLinkRequestDtoImpl
  ): Observable<any> {
    if (ppid === null || ppid === undefined) {
      throw new Error(
        'Required parameter ppid was null or undefined when calling smartChargingControllerCaptureVehicle.'
      );
    }

    if (
      vehicleLinkRequestDtoImpl === null ||
      vehicleLinkRequestDtoImpl === undefined
    ) {
      throw new Error(
        'Required parameter vehicleLinkRequestDtoImpl was null or undefined when calling smartChargingControllerCaptureVehicle.'
      );
    }

    const headers = { ...this.defaultHeaders };

    let accessTokenObservable: Observable<any> = of(null);

    // authentication (bearer) required
    if (typeof this.configuration.accessToken === 'function') {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken())
      );
    } else if (this.configuration.accessToken) {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken)
      );
    }
    // to determine the Accept header
    const httpHeaderAccepts: string[] = ['application/json'];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = ['application/json'];
    const httpContentTypeSelected: string | undefined =
      this.configuration.selectHeaderContentType(consumes);
    if (httpContentTypeSelected != undefined) {
      headers['Content-Type'] = httpContentTypeSelected;
    }
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.post<VehicleLinkResponseDtoImpl>(
          `${
            this.basePath
          }/smart-charging/delegated-controls/${encodeURIComponent(
            String(ppid)
          )}/vehicles`,
          vehicleLinkRequestDtoImpl,
          {
            withCredentials: this.configuration.withCredentials,
            headers: headers,
          }
        );
      })
    );
  }
  /**
   *
   *
   * @param ppid PPID of a given charger
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public smartChargingControllerCreateDelegatedControlStation(
    ppid: string
  ): Observable<AxiosResponse<any>>;
  public smartChargingControllerCreateDelegatedControlStation(
    ppid: string
  ): Observable<any> {
    if (ppid === null || ppid === undefined) {
      throw new Error(
        'Required parameter ppid was null or undefined when calling smartChargingControllerCreateDelegatedControlStation.'
      );
    }

    const headers = { ...this.defaultHeaders };

    let accessTokenObservable: Observable<any> = of(null);

    // authentication (bearer) required
    if (typeof this.configuration.accessToken === 'function') {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken())
      );
    } else if (this.configuration.accessToken) {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken)
      );
    }
    // to determine the Accept header
    const httpHeaderAccepts: string[] = ['application/json'];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = [];
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.put<any>(
          `${
            this.basePath
          }/smart-charging/delegated-controls/${encodeURIComponent(
            String(ppid)
          )}`,
          null,
          {
            withCredentials: this.configuration.withCredentials,
            headers: headers,
          }
        );
      })
    );
  }
  /**
   * Removes a given vehicle from a given charger
   * For a given PPID, remove a given vehicle
   * @param ppid PPID of a given charger
   * @param vehicleId ID of a given vehicle
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public smartChargingControllerDeleteVehicle(
    ppid: string,
    vehicleId: string
  ): Observable<AxiosResponse<any>>;
  public smartChargingControllerDeleteVehicle(
    ppid: string,
    vehicleId: string
  ): Observable<any> {
    if (ppid === null || ppid === undefined) {
      throw new Error(
        'Required parameter ppid was null or undefined when calling smartChargingControllerDeleteVehicle.'
      );
    }

    if (vehicleId === null || vehicleId === undefined) {
      throw new Error(
        'Required parameter vehicleId was null or undefined when calling smartChargingControllerDeleteVehicle.'
      );
    }

    const headers = { ...this.defaultHeaders };

    let accessTokenObservable: Observable<any> = of(null);

    // authentication (bearer) required
    if (typeof this.configuration.accessToken === 'function') {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken())
      );
    } else if (this.configuration.accessToken) {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken)
      );
    }
    // to determine the Accept header
    const httpHeaderAccepts: string[] = ['application/json'];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = [];
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.delete<any>(
          `${
            this.basePath
          }/smart-charging/delegated-controls/${encodeURIComponent(
            String(ppid)
          )}/vehicles/${encodeURIComponent(String(vehicleId))}`,
          {
            withCredentials: this.configuration.withCredentials,
            headers: headers,
          }
        );
      })
    );
  }
  /**
   * retrieve all vehicles and chargers for a user
   * For a given user, get all chargers and the vehicles linked to them
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public smartChargingControllerGetChargersAndVehicles(): Observable<
    AxiosResponse<ChargersAndVehicles[]>
  >;
  public smartChargingControllerGetChargersAndVehicles(): Observable<any> {
    const headers = { ...this.defaultHeaders };

    let accessTokenObservable: Observable<any> = of(null);

    // authentication (bearer) required
    if (typeof this.configuration.accessToken === 'function') {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken())
      );
    } else if (this.configuration.accessToken) {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken)
      );
    }
    // to determine the Accept header
    const httpHeaderAccepts: string[] = ['application/json'];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = [];
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.get<ChargersAndVehicles[]>(
          `${this.basePath}/smart-charging/delegated-controls/vehicles`,
          {
            withCredentials: this.configuration.withCredentials,
            headers: headers,
          }
        );
      })
    );
  }
  /**
   * retrieve intents for a charger
   * For a given PPID retrieve intents
   * @param ppid PPID of a given charger
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public smartChargingControllerGetIntents(
    ppid: string
  ): Observable<AxiosResponse<DelegatedControlChargingStationResponseDtoImpl>>;
  public smartChargingControllerGetIntents(ppid: string): Observable<any> {
    if (ppid === null || ppid === undefined) {
      throw new Error(
        'Required parameter ppid was null or undefined when calling smartChargingControllerGetIntents.'
      );
    }

    const headers = { ...this.defaultHeaders };

    let accessTokenObservable: Observable<any> = of(null);

    // authentication (bearer) required
    if (typeof this.configuration.accessToken === 'function') {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken())
      );
    } else if (this.configuration.accessToken) {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken)
      );
    }
    // to determine the Accept header
    const httpHeaderAccepts: string[] = ['application/json'];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = [];
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.get<DelegatedControlChargingStationResponseDtoImpl>(
          `${
            this.basePath
          }/smart-charging/delegated-controls/${encodeURIComponent(
            String(ppid)
          )}`,
          {
            withCredentials: this.configuration.withCredentials,
            headers: headers,
          }
        );
      })
    );
  }
  /**
   *
   *
   * @param ppid
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public smartChargingControllerRemoveChargerFromDelegatedControl(
    ppid: string
  ): Observable<AxiosResponse<any>>;
  public smartChargingControllerRemoveChargerFromDelegatedControl(
    ppid: string
  ): Observable<any> {
    if (ppid === null || ppid === undefined) {
      throw new Error(
        'Required parameter ppid was null or undefined when calling smartChargingControllerRemoveChargerFromDelegatedControl.'
      );
    }

    const headers = { ...this.defaultHeaders };

    let accessTokenObservable: Observable<any> = of(null);

    // authentication (bearer) required
    if (typeof this.configuration.accessToken === 'function') {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken())
      );
    } else if (this.configuration.accessToken) {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken)
      );
    }
    // to determine the Accept header
    const httpHeaderAccepts: string[] = ['application/json'];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = [];
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.delete<any>(
          `${
            this.basePath
          }/smart-charging/delegated-controls/${encodeURIComponent(
            String(ppid)
          )}`,
          {
            withCredentials: this.configuration.withCredentials,
            headers: headers,
          }
        );
      })
    );
  }
  /**
   * Sets delegated control intents of a given charger and vehicle
   * For a given PPID and vehicle, set delegated control intents
   * @param ppid PPID of a given charger
   * @param vehicleId
   * @param vehicleIntentsRequestDtoImpl
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public smartChargingControllerSetVehicleIntents(
    ppid: string,
    vehicleId: string,
    vehicleIntentsRequestDtoImpl: VehicleIntentsRequestDtoImpl
  ): Observable<AxiosResponse<SetVehicleIntent>>;
  public smartChargingControllerSetVehicleIntents(
    ppid: string,
    vehicleId: string,
    vehicleIntentsRequestDtoImpl: VehicleIntentsRequestDtoImpl
  ): Observable<any> {
    if (ppid === null || ppid === undefined) {
      throw new Error(
        'Required parameter ppid was null or undefined when calling smartChargingControllerSetVehicleIntents.'
      );
    }

    if (vehicleId === null || vehicleId === undefined) {
      throw new Error(
        'Required parameter vehicleId was null or undefined when calling smartChargingControllerSetVehicleIntents.'
      );
    }

    if (
      vehicleIntentsRequestDtoImpl === null ||
      vehicleIntentsRequestDtoImpl === undefined
    ) {
      throw new Error(
        'Required parameter vehicleIntentsRequestDtoImpl was null or undefined when calling smartChargingControllerSetVehicleIntents.'
      );
    }

    const headers = { ...this.defaultHeaders };

    let accessTokenObservable: Observable<any> = of(null);

    // authentication (bearer) required
    if (typeof this.configuration.accessToken === 'function') {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken())
      );
    } else if (this.configuration.accessToken) {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken)
      );
    }
    // to determine the Accept header
    const httpHeaderAccepts: string[] = ['application/json'];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = ['application/json'];
    const httpContentTypeSelected: string | undefined =
      this.configuration.selectHeaderContentType(consumes);
    if (httpContentTypeSelected != undefined) {
      headers['Content-Type'] = httpContentTypeSelected;
    }
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.put<SetVehicleIntent>(
          `${
            this.basePath
          }/smart-charging/delegated-controls/${encodeURIComponent(
            String(ppid)
          )}/vehicles/${encodeURIComponent(String(vehicleId))}/intents`,
          vehicleIntentsRequestDtoImpl,
          {
            withCredentials: this.configuration.withCredentials,
            headers: headers,
          }
        );
      })
    );
  }
  /**
   *
   *
   * @param ppid PPID of a given charger
   * @param vehicleId ID of a given vehicle
   * @param updateVehicleLinkRequestDtoImpl
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public smartChargingControllerUpdateVehicle(
    ppid: string,
    vehicleId: string,
    updateVehicleLinkRequestDtoImpl: UpdateVehicleLinkRequestDtoImpl
  ): Observable<AxiosResponse<SmartChargingControllerUpdateVehicle200Response>>;
  public smartChargingControllerUpdateVehicle(
    ppid: string,
    vehicleId: string,
    updateVehicleLinkRequestDtoImpl: UpdateVehicleLinkRequestDtoImpl
  ): Observable<any> {
    if (ppid === null || ppid === undefined) {
      throw new Error(
        'Required parameter ppid was null or undefined when calling smartChargingControllerUpdateVehicle.'
      );
    }

    if (vehicleId === null || vehicleId === undefined) {
      throw new Error(
        'Required parameter vehicleId was null or undefined when calling smartChargingControllerUpdateVehicle.'
      );
    }

    if (
      updateVehicleLinkRequestDtoImpl === null ||
      updateVehicleLinkRequestDtoImpl === undefined
    ) {
      throw new Error(
        'Required parameter updateVehicleLinkRequestDtoImpl was null or undefined when calling smartChargingControllerUpdateVehicle.'
      );
    }

    const headers = { ...this.defaultHeaders };

    let accessTokenObservable: Observable<any> = of(null);

    // authentication (bearer) required
    if (typeof this.configuration.accessToken === 'function') {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken())
      );
    } else if (this.configuration.accessToken) {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken)
      );
    }
    // to determine the Accept header
    const httpHeaderAccepts: string[] = ['application/json'];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = ['application/json'];
    const httpContentTypeSelected: string | undefined =
      this.configuration.selectHeaderContentType(consumes);
    if (httpContentTypeSelected != undefined) {
      headers['Content-Type'] = httpContentTypeSelected;
    }
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.patch<SmartChargingControllerUpdateVehicle200Response>(
          `${
            this.basePath
          }/smart-charging/delegated-controls/${encodeURIComponent(
            String(ppid)
          )}/vehicles/${encodeURIComponent(String(vehicleId))}`,
          updateVehicleLinkRequestDtoImpl,
          {
            withCredentials: this.configuration.withCredentials,
            headers: headers,
          }
        );
      })
    );
  }
}
