/**
 * Mobile API
 * Mobile Backend for Frontend API service
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
/* tslint:disable:no-unused-variable member-ordering */

import { Injectable, Optional } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { AxiosResponse } from 'axios';
import { Observable, of, switchMap } from 'rxjs';
import { CheckForUpgrade } from '../model/checkForUpgrade';
import { Configuration } from '../configuration';

@Injectable()
export class CheckForUpgradeService {
  protected basePath = 'http://localhost';
  public defaultHeaders: Record<string, string> = {};
  public configuration = new Configuration();
  protected httpClient: HttpService;

  constructor(
    httpClient: HttpService,
    @Optional() configuration: Configuration
  ) {
    this.configuration = configuration || this.configuration;
    this.basePath = configuration?.basePath || this.basePath;
    this.httpClient = configuration?.httpClient || httpClient;
  }

  /**
   * @param consumes string[] mime-types
   * @return true: consumes contains 'multipart/form-data', false: otherwise
   */
  private canConsumeForm(consumes: string[]): boolean {
    const form = 'multipart/form-data';
    return consumes.includes(form);
  }

  /**
   * returns whether an app version needs to be upgraded or not
   * For a given version number returns whether a new update is available and upgrade is required
   * @param appName The app name
   * @param appVersion The app version
   * @param platform The app platform
   * @param environment The app environment
   * @param appLanguage The app language
   * @param xApiKey x-api-key for your project
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public checkForUpgradeControllerCheckForUpgrade(
    appName: string,
    appVersion: string,
    platform: string,
    environment: string,
    appLanguage: string,
    xApiKey?: string
  ): Observable<AxiosResponse<CheckForUpgrade>>;
  public checkForUpgradeControllerCheckForUpgrade(
    appName: string,
    appVersion: string,
    platform: string,
    environment: string,
    appLanguage: string,
    xApiKey?: string
  ): Observable<any> {
    if (appName === null || appName === undefined) {
      throw new Error(
        'Required parameter appName was null or undefined when calling checkForUpgradeControllerCheckForUpgrade.'
      );
    }

    if (appVersion === null || appVersion === undefined) {
      throw new Error(
        'Required parameter appVersion was null or undefined when calling checkForUpgradeControllerCheckForUpgrade.'
      );
    }

    if (platform === null || platform === undefined) {
      throw new Error(
        'Required parameter platform was null or undefined when calling checkForUpgradeControllerCheckForUpgrade.'
      );
    }

    if (environment === null || environment === undefined) {
      throw new Error(
        'Required parameter environment was null or undefined when calling checkForUpgradeControllerCheckForUpgrade.'
      );
    }

    if (appLanguage === null || appLanguage === undefined) {
      throw new Error(
        'Required parameter appLanguage was null or undefined when calling checkForUpgradeControllerCheckForUpgrade.'
      );
    }

    const queryParameters = new URLSearchParams();
    if (appName !== undefined && appName !== null) {
      queryParameters.append('app_name', <any>appName);
    }
    if (appVersion !== undefined && appVersion !== null) {
      queryParameters.append('app_version', <any>appVersion);
    }
    if (platform !== undefined && platform !== null) {
      queryParameters.append('platform', <any>platform);
    }
    if (environment !== undefined && environment !== null) {
      queryParameters.append('environment', <any>environment);
    }
    if (appLanguage !== undefined && appLanguage !== null) {
      queryParameters.append('app_language', <any>appLanguage);
    }

    const headers = { ...this.defaultHeaders };
    if (xApiKey !== undefined && xApiKey !== null) {
      headers['x-api-key'] = String(xApiKey);
    }

    const accessTokenObservable: Observable<any> = of(null);

    // to determine the Accept header
    const httpHeaderAccepts: string[] = ['application/json'];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = [];
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.get<CheckForUpgrade>(
          `${this.basePath}/check-for-upgrade`,
          {
            params: queryParameters,
            withCredentials: this.configuration.withCredentials,
            headers: headers,
          }
        );
      })
    );
  }
}
