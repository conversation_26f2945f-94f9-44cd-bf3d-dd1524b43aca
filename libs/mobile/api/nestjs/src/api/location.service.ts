/**
 * Mobile API
 * Mobile Backend for Frontend API service
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
/* tslint:disable:no-unused-variable member-ordering */

import { Injectable, Optional } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { AxiosResponse } from 'axios';
import { Observable, from, of, switchMap } from 'rxjs';
import { Address } from '../model/address';
import { Item } from '../model/item';
import { Configuration } from '../configuration';

@Injectable()
export class LocationService {
  protected basePath = 'http://localhost';
  public defaultHeaders: Record<string, string> = {};
  public configuration = new Configuration();
  protected httpClient: HttpService;

  constructor(
    httpClient: HttpService,
    @Optional() configuration: Configuration
  ) {
    this.configuration = configuration || this.configuration;
    this.basePath = configuration?.basePath || this.basePath;
    this.httpClient = configuration?.httpClient || httpClient;
  }

  /**
   * @param consumes string[] mime-types
   * @return true: consumes contains 'multipart/form-data', false: otherwise
   */
  private canConsumeForm(consumes: string[]): boolean {
    const form = 'multipart/form-data';
    return consumes.includes(form);
  }

  /**
   * find the address
   * Returns the full address details based on the Id.
   * @param id The ID from a search result to retrieve the details for.
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public locationControllerFind(
    id: string
  ): Observable<AxiosResponse<Address[]>>;
  public locationControllerFind(id: string): Observable<any> {
    if (id === null || id === undefined) {
      throw new Error(
        'Required parameter id was null or undefined when calling locationControllerFind.'
      );
    }

    const queryParameters = new URLSearchParams();
    if (id !== undefined && id !== null) {
      queryParameters.append('id', <any>id);
    }

    const headers = { ...this.defaultHeaders };

    let accessTokenObservable: Observable<any> = of(null);

    // authentication (bearer) required
    if (typeof this.configuration.accessToken === 'function') {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken())
      );
    } else if (this.configuration.accessToken) {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken)
      );
    }
    // to determine the Accept header
    const httpHeaderAccepts: string[] = ['application/json'];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = [];
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.get<Address[]>(
          `${this.basePath}/location/find`,
          {
            params: queryParameters,
            withCredentials: this.configuration.withCredentials,
            headers: headers,
          }
        );
      })
    );
  }
  /**
   * search for an address
   * Partial address returned.
   * @param text The search text to find. Ideally a postcode or the start of the address.
   * @param acceptLanguage
   * @param containerId A container for the search. This should only be another Id previously returned from this service when the Type of the result was not \&quot;Address\&quot;.
   * @param language The preferred language for results. This should be a 2 or 4 character language code e.g. (en, fr, en-gb, en-us etc). Falls back to Accept-Language header if not set and defaults to \&quot;en\&quot; if neither are present.
   * @param limit The maximum number of results to return.
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public locationControllerSearch(
    text: string,
    acceptLanguage: string,
    containerId?: string,
    language?: string,
    limit?: number
  ): Observable<AxiosResponse<Item[]>>;
  public locationControllerSearch(
    text: string,
    acceptLanguage: string,
    containerId?: string,
    language?: string,
    limit?: number
  ): Observable<any> {
    if (text === null || text === undefined) {
      throw new Error(
        'Required parameter text was null or undefined when calling locationControllerSearch.'
      );
    }

    if (acceptLanguage === null || acceptLanguage === undefined) {
      throw new Error(
        'Required parameter acceptLanguage was null or undefined when calling locationControllerSearch.'
      );
    }

    const queryParameters = new URLSearchParams();
    if (text !== undefined && text !== null) {
      queryParameters.append('text', <any>text);
    }
    if (containerId !== undefined && containerId !== null) {
      queryParameters.append('containerId', <any>containerId);
    }
    if (language !== undefined && language !== null) {
      queryParameters.append('language', <any>language);
    }
    if (limit !== undefined && limit !== null) {
      queryParameters.append('limit', <any>limit);
    }

    const headers = { ...this.defaultHeaders };
    if (acceptLanguage !== undefined && acceptLanguage !== null) {
      headers['Accept-Language'] = String(acceptLanguage);
    }

    let accessTokenObservable: Observable<any> = of(null);

    // authentication (bearer) required
    if (typeof this.configuration.accessToken === 'function') {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken())
      );
    } else if (this.configuration.accessToken) {
      accessTokenObservable = from(
        Promise.resolve(this.configuration.accessToken)
      );
    }
    // to determine the Accept header
    const httpHeaderAccepts: string[] = ['application/json'];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = [];
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.get<Item[]>(
          `${this.basePath}/location/search`,
          {
            params: queryParameters,
            withCredentials: this.configuration.withCredentials,
            headers: headers,
          }
        );
      })
    );
  }
}
