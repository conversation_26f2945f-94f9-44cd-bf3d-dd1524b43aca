 
const baseTailwindConfig = require('./tailwind.config');

module.exports = {
  content: ['./**/*.html'],
  presets: [baseTailwindConfig],
  theme: {
    extend: {
      colors: {
        primary: {
          400: '#C8D72D',
          800: '#005F41',
        },
      },
    },
  },
  corePlugins: {
    preflight: false,
    backgroundOpacity: false,
    borderOpacity: false,
    boxShadow: false,
    divideOpacity: false,
    placeholderOpacity: false,
    textOpacity: false,
  },
  plugins: [
    import('@tailwindcss/forms'),
    import('tailwindcss-box-shadow'),
    import('tailwindcss-email-variants'),
    import('tailwindcss-mso'),
  ],
};
