/**
 * Driver Account API
 * Driver account API service
 *
 * The version of the OpenAPI document: 1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
/* tslint:disable:no-unused-variable member-ordering */

import { Injectable, Optional } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { AxiosResponse } from 'axios';
import { Observable, of, switchMap } from 'rxjs';
import { CreateUserDto } from '../model/createUserDto';
import { CreateUserResponseDto } from '../model/createUserResponseDto';
import { ExtendedUserInfoResponseDto } from '../model/extendedUserInfoResponseDto';
import { TrackLoginRequest } from '../model/trackLoginRequest';
import { UpdateUserSuppressedStatusDto } from '../model/updateUserSuppressedStatusDto';
import { UserChargerDto } from '../model/userChargerDto';
import { UserDetailsDto } from '../model/userDetailsDto';
import { UserEmailDto } from '../model/userEmailDto';
import { UserInfoResponseDto } from '../model/userInfoResponseDto';
import { UserSuppressedStatusDto } from '../model/userSuppressedStatusDto';
import { Configuration } from '../configuration';

@Injectable()
export class UsersService {
  protected basePath = 'http://localhost';
  public defaultHeaders: Record<string, string> = {};
  public configuration = new Configuration();
  protected httpClient: HttpService;

  constructor(
    httpClient: HttpService,
    @Optional() configuration: Configuration
  ) {
    this.configuration = configuration || this.configuration;
    this.basePath = configuration?.basePath || this.basePath;
    this.httpClient = configuration?.httpClient || httpClient;
  }

  /**
   * @param consumes string[] mime-types
   * @return true: consumes contains 'multipart/form-data', false: otherwise
   */
  private canConsumeForm(consumes: string[]): boolean {
    const form = 'multipart/form-data';
    return consumes.includes(form);
  }

  /**
   * create a new user
   * Creates a new user account
   * @param createUserDto
   * @param resetPasswordContinueUrl Reset password continue url that is optional
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public userControllerCreateUser(
    createUserDto: CreateUserDto,
    resetPasswordContinueUrl?: string
  ): Observable<AxiosResponse<CreateUserResponseDto>>;
  public userControllerCreateUser(
    createUserDto: CreateUserDto,
    resetPasswordContinueUrl?: string
  ): Observable<any> {
    if (createUserDto === null || createUserDto === undefined) {
      throw new Error(
        'Required parameter createUserDto was null or undefined when calling userControllerCreateUser.'
      );
    }

    const queryParameters = new URLSearchParams();
    if (
      resetPasswordContinueUrl !== undefined &&
      resetPasswordContinueUrl !== null
    ) {
      queryParameters.append(
        'reset_password_continue_url',
        <any>resetPasswordContinueUrl
      );
    }

    const headers = { ...this.defaultHeaders };

    const accessTokenObservable: Observable<any> = of(null);

    // to determine the Accept header
    const httpHeaderAccepts: string[] = ['application/json'];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = ['application/json'];
    const httpContentTypeSelected: string | undefined =
      this.configuration.selectHeaderContentType(consumes);
    if (httpContentTypeSelected != undefined) {
      headers['Content-Type'] = httpContentTypeSelected;
    }
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.post<CreateUserResponseDto>(
          `${this.basePath}/v1/users`,
          createUserDto,
          {
            params: queryParameters,
            withCredentials: this.configuration.withCredentials,
            headers: headers,
          }
        );
      })
    );
  }
  /**
   * enable user profile by UID
   * Enable a user profile for a given UID
   * @param uid
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public userControllerEnable(
    uid: string
  ): Observable<AxiosResponse<UserInfoResponseDto>>;
  public userControllerEnable(uid: string): Observable<any> {
    if (uid === null || uid === undefined) {
      throw new Error(
        'Required parameter uid was null or undefined when calling userControllerEnable.'
      );
    }

    const headers = { ...this.defaultHeaders };

    const accessTokenObservable: Observable<any> = of(null);

    // to determine the Accept header
    const httpHeaderAccepts: string[] = ['application/json'];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = [];
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.put<UserInfoResponseDto>(
          `${this.basePath}/v1/users/enable/${encodeURIComponent(String(uid))}`,
          null,
          {
            withCredentials: this.configuration.withCredentials,
            headers: headers,
          }
        );
      })
    );
  }
  /**
   * get user profile by given filters
   * Get a user profile by a given filter. Accepts only email at the moment
   * @param ppid Allows for searching by PPID
   * @param emailLike Allows for fuzzy matching on the email field
   * @param email
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public userControllerGetByFilter(
    ppid?: string,
    emailLike?: string,
    email?: string
  ): Observable<AxiosResponse<UserInfoResponseDto[]>>;
  public userControllerGetByFilter(
    ppid?: string,
    emailLike?: string,
    email?: string
  ): Observable<any> {
    const queryParameters = new URLSearchParams();
    if (ppid !== undefined && ppid !== null) {
      queryParameters.append('ppid', <any>ppid);
    }
    if (emailLike !== undefined && emailLike !== null) {
      queryParameters.append('emailLike', <any>emailLike);
    }
    if (email !== undefined && email !== null) {
      queryParameters.append('email', <any>email);
    }

    const headers = { ...this.defaultHeaders };

    const accessTokenObservable: Observable<any> = of(null);

    // to determine the Accept header
    const httpHeaderAccepts: string[] = ['application/json'];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = [];
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.get<UserInfoResponseDto[]>(
          `${this.basePath}/v1/users`,
          {
            params: queryParameters,
            withCredentials: this.configuration.withCredentials,
            headers: headers,
          }
        );
      })
    );
  }
  /**
   * get user\&#39;s chargers by their UID
   * Get a user\&#39;s chargers by their  UID
   * @param uid
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public userControllerGetChargers(
    uid: string
  ): Observable<AxiosResponse<UserChargerDto[]>>;
  public userControllerGetChargers(uid: string): Observable<any> {
    if (uid === null || uid === undefined) {
      throw new Error(
        'Required parameter uid was null or undefined when calling userControllerGetChargers.'
      );
    }

    const headers = { ...this.defaultHeaders };

    const accessTokenObservable: Observable<any> = of(null);

    // to determine the Accept header
    const httpHeaderAccepts: string[] = ['application/json'];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = [];
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.get<UserChargerDto[]>(
          `${this.basePath}/v1/users/${encodeURIComponent(
            String(uid)
          )}/chargers`,
          {
            withCredentials: this.configuration.withCredentials,
            headers: headers,
          }
        );
      })
    );
  }
  /**
   * get a user\&#39;s suppressed status by their UID
   * get a user\&#39;s suppressed status by their UID
   * @param uid
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public userControllerGetSuppressedStatus(
    uid: string
  ): Observable<AxiosResponse<UserSuppressedStatusDto>>;
  public userControllerGetSuppressedStatus(uid: string): Observable<any> {
    if (uid === null || uid === undefined) {
      throw new Error(
        'Required parameter uid was null or undefined when calling userControllerGetSuppressedStatus.'
      );
    }

    const headers = { ...this.defaultHeaders };

    const accessTokenObservable: Observable<any> = of(null);

    // to determine the Accept header
    const httpHeaderAccepts: string[] = ['application/json'];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = [];
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.get<UserSuppressedStatusDto>(
          `${this.basePath}/v1/users/${encodeURIComponent(
            String(uid)
          )}/suppressedStatus`,
          {
            withCredentials: this.configuration.withCredentials,
            headers: headers,
          }
        );
      })
    );
  }
  /**
   * get user profile by UID
   * Get a user profile for a given UID
   * @param uid
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public userControllerGetUser(
    uid: string
  ): Observable<AxiosResponse<ExtendedUserInfoResponseDto>>;
  public userControllerGetUser(uid: string): Observable<any> {
    if (uid === null || uid === undefined) {
      throw new Error(
        'Required parameter uid was null or undefined when calling userControllerGetUser.'
      );
    }

    const headers = { ...this.defaultHeaders };

    const accessTokenObservable: Observable<any> = of(null);

    // to determine the Accept header
    const httpHeaderAccepts: string[] = ['application/json'];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = [];
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.get<ExtendedUserInfoResponseDto>(
          `${this.basePath}/v1/users/${encodeURIComponent(String(uid))}`,
          {
            withCredentials: this.configuration.withCredentials,
            headers: headers,
          }
        );
      })
    );
  }
  /**
   * delete a user
   * delete a user
   * @param uid
   * @param force Allows to disable users when they have outstanding balance
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public userControllerSoftDelete(
    uid: string,
    force?: boolean
  ): Observable<AxiosResponse<any>>;
  public userControllerSoftDelete(
    uid: string,
    force?: boolean
  ): Observable<any> {
    if (uid === null || uid === undefined) {
      throw new Error(
        'Required parameter uid was null or undefined when calling userControllerSoftDelete.'
      );
    }

    const queryParameters = new URLSearchParams();
    if (force !== undefined && force !== null) {
      queryParameters.append('force', <any>force);
    }

    const headers = { ...this.defaultHeaders };

    const accessTokenObservable: Observable<any> = of(null);

    // to determine the Accept header
    const httpHeaderAccepts: string[] = [];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = [];
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.delete<any>(
          `${this.basePath}/v1/users/${encodeURIComponent(String(uid))}`,
          {
            params: queryParameters,
            withCredentials: this.configuration.withCredentials,
            headers: headers,
          }
        );
      })
    );
  }
  /**
   * Generates an email of when and from which ip the user logged in
   * Generates an email of when and from which ip the user logged in
   * @param uid
   * @param trackLoginRequest
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public userControllerTrackLogin(
    uid: string,
    trackLoginRequest: TrackLoginRequest
  ): Observable<AxiosResponse<any>>;
  public userControllerTrackLogin(
    uid: string,
    trackLoginRequest: TrackLoginRequest
  ): Observable<any> {
    if (uid === null || uid === undefined) {
      throw new Error(
        'Required parameter uid was null or undefined when calling userControllerTrackLogin.'
      );
    }

    if (trackLoginRequest === null || trackLoginRequest === undefined) {
      throw new Error(
        'Required parameter trackLoginRequest was null or undefined when calling userControllerTrackLogin.'
      );
    }

    const headers = { ...this.defaultHeaders };

    const accessTokenObservable: Observable<any> = of(null);

    // to determine the Accept header
    const httpHeaderAccepts: string[] = [];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = ['application/json'];
    const httpContentTypeSelected: string | undefined =
      this.configuration.selectHeaderContentType(consumes);
    if (httpContentTypeSelected != undefined) {
      headers['Content-Type'] = httpContentTypeSelected;
    }
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.post<any>(
          `${this.basePath}/v1/users/login/${encodeURIComponent(
            String(uid)
          )}/alert`,
          trackLoginRequest,
          {
            withCredentials: this.configuration.withCredentials,
            headers: headers,
          }
        );
      })
    );
  }
  /**
   * unlink a charger from a user
   * Unlink a charger from a user
   * @param uid
   * @param ppid
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public userControllerUnLinkCharger(
    uid: string,
    ppid: string
  ): Observable<AxiosResponse<any>>;
  public userControllerUnLinkCharger(
    uid: string,
    ppid: string
  ): Observable<any> {
    if (uid === null || uid === undefined) {
      throw new Error(
        'Required parameter uid was null or undefined when calling userControllerUnLinkCharger.'
      );
    }

    if (ppid === null || ppid === undefined) {
      throw new Error(
        'Required parameter ppid was null or undefined when calling userControllerUnLinkCharger.'
      );
    }

    const headers = { ...this.defaultHeaders };

    const accessTokenObservable: Observable<any> = of(null);

    // to determine the Accept header
    const httpHeaderAccepts: string[] = [];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = [];
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.delete<any>(
          `${this.basePath}/v1/users/${encodeURIComponent(
            String(uid)
          )}/chargers/${encodeURIComponent(String(ppid))}`,
          {
            withCredentials: this.configuration.withCredentials,
            headers: headers,
          }
        );
      })
    );
  }
  /**
   * update user email
   * Update a user email for an existing email and new email
   * @param userEmailDto
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public userControllerUpdateEmail(
    userEmailDto: UserEmailDto
  ): Observable<AxiosResponse<UserEmailDto>>;
  public userControllerUpdateEmail(
    userEmailDto: UserEmailDto
  ): Observable<any> {
    if (userEmailDto === null || userEmailDto === undefined) {
      throw new Error(
        'Required parameter userEmailDto was null or undefined when calling userControllerUpdateEmail.'
      );
    }

    const headers = { ...this.defaultHeaders };

    const accessTokenObservable: Observable<any> = of(null);

    // to determine the Accept header
    const httpHeaderAccepts: string[] = ['application/json'];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = ['application/json'];
    const httpContentTypeSelected: string | undefined =
      this.configuration.selectHeaderContentType(consumes);
    if (httpContentTypeSelected != undefined) {
      headers['Content-Type'] = httpContentTypeSelected;
    }
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.put<UserEmailDto>(
          `${this.basePath}/v1/users/email/update`,
          userEmailDto,
          {
            withCredentials: this.configuration.withCredentials,
            headers: headers,
          }
        );
      })
    );
  }
  /**
   * remove a user from the SES suppression list
   * remove a user from the SES suppression list
   * @param uid
   * @param updateUserSuppressedStatusDto
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public userControllerUpdateSuppressedStatus(
    uid: string,
    updateUserSuppressedStatusDto: UpdateUserSuppressedStatusDto
  ): Observable<AxiosResponse<UpdateUserSuppressedStatusDto>>;
  public userControllerUpdateSuppressedStatus(
    uid: string,
    updateUserSuppressedStatusDto: UpdateUserSuppressedStatusDto
  ): Observable<any> {
    if (uid === null || uid === undefined) {
      throw new Error(
        'Required parameter uid was null or undefined when calling userControllerUpdateSuppressedStatus.'
      );
    }

    if (
      updateUserSuppressedStatusDto === null ||
      updateUserSuppressedStatusDto === undefined
    ) {
      throw new Error(
        'Required parameter updateUserSuppressedStatusDto was null or undefined when calling userControllerUpdateSuppressedStatus.'
      );
    }

    const headers = { ...this.defaultHeaders };

    const accessTokenObservable: Observable<any> = of(null);

    // to determine the Accept header
    const httpHeaderAccepts: string[] = ['application/json'];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = ['application/json'];
    const httpContentTypeSelected: string | undefined =
      this.configuration.selectHeaderContentType(consumes);
    if (httpContentTypeSelected != undefined) {
      headers['Content-Type'] = httpContentTypeSelected;
    }
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.put<UpdateUserSuppressedStatusDto>(
          `${this.basePath}/v1/users/${encodeURIComponent(
            String(uid)
          )}/suppressedStatus`,
          updateUserSuppressedStatusDto,
          {
            withCredentials: this.configuration.withCredentials,
            headers: headers,
          }
        );
      })
    );
  }
  /**
   * update user profile by UID
   * Update a user profile for a given UID
   * @param uid
   * @param userDetailsDto
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public userControllerUpdateUser(
    uid: string,
    userDetailsDto: UserDetailsDto
  ): Observable<AxiosResponse<UserInfoResponseDto>>;
  public userControllerUpdateUser(
    uid: string,
    userDetailsDto: UserDetailsDto
  ): Observable<any> {
    if (uid === null || uid === undefined) {
      throw new Error(
        'Required parameter uid was null or undefined when calling userControllerUpdateUser.'
      );
    }

    if (userDetailsDto === null || userDetailsDto === undefined) {
      throw new Error(
        'Required parameter userDetailsDto was null or undefined when calling userControllerUpdateUser.'
      );
    }

    const headers = { ...this.defaultHeaders };

    const accessTokenObservable: Observable<any> = of(null);

    // to determine the Accept header
    const httpHeaderAccepts: string[] = ['application/json'];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = ['application/json'];
    const httpContentTypeSelected: string | undefined =
      this.configuration.selectHeaderContentType(consumes);
    if (httpContentTypeSelected != undefined) {
      headers['Content-Type'] = httpContentTypeSelected;
    }
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.put<UserInfoResponseDto>(
          `${this.basePath}/v1/users/${encodeURIComponent(String(uid))}`,
          userDetailsDto,
          {
            withCredentials: this.configuration.withCredentials,
            headers: headers,
          }
        );
      })
    );
  }
}
