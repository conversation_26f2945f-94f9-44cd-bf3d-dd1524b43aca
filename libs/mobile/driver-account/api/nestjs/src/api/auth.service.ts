/**
 * Driver Account API
 * Driver account API service
 *
 * The version of the OpenAPI document: 1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
/* tslint:disable:no-unused-variable member-ordering */

import { Injectable, Optional } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { AxiosResponse } from 'axios';
import { Observable, of, switchMap } from 'rxjs';
import { Factor } from '../model/factor';
import { RandomisePasswordRequest } from '../model/randomisePasswordRequest';
import { RemoveFactorRequest } from '../model/removeFactorRequest';
import { SendEmailVerificationRequest } from '../model/sendEmailVerificationRequest';
import { SendPasswordResetRequest } from '../model/sendPasswordResetRequest';
import { SendRecoverFactorRequest } from '../model/sendRecoverFactorRequest';
import { SendSignInWithEmailRequest } from '../model/sendSignInWithEmailRequest';
import { SendVerifyAndChangeEmailRequest } from '../model/sendVerifyAndChangeEmailRequest';
import { Configuration } from '../configuration';

@Injectable()
export class AuthService {
  protected basePath = 'http://localhost';
  public defaultHeaders: Record<string, string> = {};
  public configuration = new Configuration();
  protected httpClient: HttpService;

  constructor(
    httpClient: HttpService,
    @Optional() configuration: Configuration
  ) {
    this.configuration = configuration || this.configuration;
    this.basePath = configuration?.basePath || this.basePath;
    this.httpClient = configuration?.httpClient || httpClient;
  }

  /**
   * @param consumes string[] mime-types
   * @return true: consumes contains 'multipart/form-data', false: otherwise
   */
  private canConsumeForm(consumes: string[]): boolean {
    const form = 'multipart/form-data';
    return consumes.includes(form);
  }

  /**
   * verify a user\&#39;s email address
   * Sends an email verification email to the user\&#39;s email address
   * @param sendEmailVerificationRequest
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public emailVerificationControllerSendEmailVerification(
    sendEmailVerificationRequest: SendEmailVerificationRequest
  ): Observable<AxiosResponse<any>>;
  public emailVerificationControllerSendEmailVerification(
    sendEmailVerificationRequest: SendEmailVerificationRequest
  ): Observable<any> {
    if (
      sendEmailVerificationRequest === null ||
      sendEmailVerificationRequest === undefined
    ) {
      throw new Error(
        'Required parameter sendEmailVerificationRequest was null or undefined when calling emailVerificationControllerSendEmailVerification.'
      );
    }

    const headers = { ...this.defaultHeaders };

    const accessTokenObservable: Observable<any> = of(null);

    // to determine the Accept header
    const httpHeaderAccepts: string[] = [];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = ['application/json'];
    const httpContentTypeSelected: string | undefined =
      this.configuration.selectHeaderContentType(consumes);
    if (httpContentTypeSelected != undefined) {
      headers['Content-Type'] = httpContentTypeSelected;
    }
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.post<any>(
          `${this.basePath}/v1/auth/email-verification`,
          sendEmailVerificationRequest,
          {
            withCredentials: this.configuration.withCredentials,
            headers: headers,
          }
        );
      })
    );
  }
  /**
   * reset a user\&#39;s password
   * Sends a reset password email to the user\&#39;s email address
   * @param sendPasswordResetRequest
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public passwordResetControllerSendPasswordReset(
    sendPasswordResetRequest: SendPasswordResetRequest
  ): Observable<AxiosResponse<any>>;
  public passwordResetControllerSendPasswordReset(
    sendPasswordResetRequest: SendPasswordResetRequest
  ): Observable<any> {
    if (
      sendPasswordResetRequest === null ||
      sendPasswordResetRequest === undefined
    ) {
      throw new Error(
        'Required parameter sendPasswordResetRequest was null or undefined when calling passwordResetControllerSendPasswordReset.'
      );
    }

    const headers = { ...this.defaultHeaders };

    const accessTokenObservable: Observable<any> = of(null);

    // to determine the Accept header
    const httpHeaderAccepts: string[] = [];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = ['application/json'];
    const httpContentTypeSelected: string | undefined =
      this.configuration.selectHeaderContentType(consumes);
    if (httpContentTypeSelected != undefined) {
      headers['Content-Type'] = httpContentTypeSelected;
    }
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.post<any>(
          `${this.basePath}/v1/auth/password-reset`,
          sendPasswordResetRequest,
          {
            withCredentials: this.configuration.withCredentials,
            headers: headers,
          }
        );
      })
    );
  }
  /**
   * Sends a given email an alert to say that their password has updated
   * For a given email, notify them of their password being updated
   * @param body
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public passwordResetControllerSendPasswordResetAlert(
    body: object
  ): Observable<AxiosResponse<any>>;
  public passwordResetControllerSendPasswordResetAlert(
    body: object
  ): Observable<any> {
    if (body === null || body === undefined) {
      throw new Error(
        'Required parameter body was null or undefined when calling passwordResetControllerSendPasswordResetAlert.'
      );
    }

    const headers = { ...this.defaultHeaders };

    const accessTokenObservable: Observable<any> = of(null);

    // to determine the Accept header
    const httpHeaderAccepts: string[] = [];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = ['application/json'];
    const httpContentTypeSelected: string | undefined =
      this.configuration.selectHeaderContentType(consumes);
    if (httpContentTypeSelected != undefined) {
      headers['Content-Type'] = httpContentTypeSelected;
    }
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.post<any>(
          `${this.basePath}/v1/auth/password-reset-alert`,
          body,
          {
            withCredentials: this.configuration.withCredentials,
            headers: headers,
          }
        );
      })
    );
  }
  /**
   * randomise user password
   * Secure an account by randomising the password
   * @param randomisePasswordRequest
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public randomisePasswordControllerRandomisePassword(
    randomisePasswordRequest: RandomisePasswordRequest
  ): Observable<AxiosResponse<any>>;
  public randomisePasswordControllerRandomisePassword(
    randomisePasswordRequest: RandomisePasswordRequest
  ): Observable<any> {
    if (
      randomisePasswordRequest === null ||
      randomisePasswordRequest === undefined
    ) {
      throw new Error(
        'Required parameter randomisePasswordRequest was null or undefined when calling randomisePasswordControllerRandomisePassword.'
      );
    }

    const headers = { ...this.defaultHeaders };

    const accessTokenObservable: Observable<any> = of(null);

    // to determine the Accept header
    const httpHeaderAccepts: string[] = [];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = ['application/json'];
    const httpContentTypeSelected: string | undefined =
      this.configuration.selectHeaderContentType(consumes);
    if (httpContentTypeSelected != undefined) {
      headers['Content-Type'] = httpContentTypeSelected;
    }
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.post<any>(
          `${this.basePath}/v1/auth/randomise-password`,
          randomisePasswordRequest,
          {
            withCredentials: this.configuration.withCredentials,
            headers: headers,
          }
        );
      })
    );
  }
  /**
   * recover a user\&#39;s factor
   * Sends a recover factor email to the user\&#39;s email address
   * @param sendRecoverFactorRequest
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public recoverFactorControllerSendRecoverFactor(
    sendRecoverFactorRequest: SendRecoverFactorRequest
  ): Observable<AxiosResponse<any>>;
  public recoverFactorControllerSendRecoverFactor(
    sendRecoverFactorRequest: SendRecoverFactorRequest
  ): Observable<any> {
    if (
      sendRecoverFactorRequest === null ||
      sendRecoverFactorRequest === undefined
    ) {
      throw new Error(
        'Required parameter sendRecoverFactorRequest was null or undefined when calling recoverFactorControllerSendRecoverFactor.'
      );
    }

    const headers = { ...this.defaultHeaders };

    const accessTokenObservable: Observable<any> = of(null);

    // to determine the Accept header
    const httpHeaderAccepts: string[] = [];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = ['application/json'];
    const httpContentTypeSelected: string | undefined =
      this.configuration.selectHeaderContentType(consumes);
    if (httpContentTypeSelected != undefined) {
      headers['Content-Type'] = httpContentTypeSelected;
    }
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.post<any>(
          `${this.basePath}/v1/auth/recover-factor`,
          sendRecoverFactorRequest,
          {
            withCredentials: this.configuration.withCredentials,
            headers: headers,
          }
        );
      })
    );
  }
  /**
   * remove factor
   * Remove factor from an account
   * @param removeFactorRequest
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public removeFactorControllerRemoveFactor(
    removeFactorRequest: RemoveFactorRequest
  ): Observable<AxiosResponse<any>>;
  public removeFactorControllerRemoveFactor(
    removeFactorRequest: RemoveFactorRequest
  ): Observable<any> {
    if (removeFactorRequest === null || removeFactorRequest === undefined) {
      throw new Error(
        'Required parameter removeFactorRequest was null or undefined when calling removeFactorControllerRemoveFactor.'
      );
    }

    const headers = { ...this.defaultHeaders };

    const accessTokenObservable: Observable<any> = of(null);

    // to determine the Accept header
    const httpHeaderAccepts: string[] = [];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = ['application/json'];
    const httpContentTypeSelected: string | undefined =
      this.configuration.selectHeaderContentType(consumes);
    if (httpContentTypeSelected != undefined) {
      headers['Content-Type'] = httpContentTypeSelected;
    }
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.delete<any>(`${this.basePath}/v1/auth/factor`, {
          withCredentials: this.configuration.withCredentials,
          headers: headers,
        });
      })
    );
  }
  /**
   * remove factor by id
   * Remove factor for a given authId and factorId
   * @param authId
   * @param factorId
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public removeFactorControllerRemoveFactorById(
    authId: string,
    factorId: string
  ): Observable<AxiosResponse<any>>;
  public removeFactorControllerRemoveFactorById(
    authId: string,
    factorId: string
  ): Observable<any> {
    if (authId === null || authId === undefined) {
      throw new Error(
        'Required parameter authId was null or undefined when calling removeFactorControllerRemoveFactorById.'
      );
    }

    if (factorId === null || factorId === undefined) {
      throw new Error(
        'Required parameter factorId was null or undefined when calling removeFactorControllerRemoveFactorById.'
      );
    }

    const headers = { ...this.defaultHeaders };

    const accessTokenObservable: Observable<any> = of(null);

    // to determine the Accept header
    const httpHeaderAccepts: string[] = [];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = [];
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.delete<any>(
          `${this.basePath}/v1/auth/factor/${encodeURIComponent(
            String(authId)
          )}/${encodeURIComponent(String(factorId))}`,
          {
            withCredentials: this.configuration.withCredentials,
            headers: headers,
          }
        );
      })
    );
  }
  /**
   * get factors
   * Retrieve factors for a given authId
   * @param authId
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public retrieveFactorControllerGetFactors(
    authId: string
  ): Observable<AxiosResponse<Factor[]>>;
  public retrieveFactorControllerGetFactors(authId: string): Observable<any> {
    if (authId === null || authId === undefined) {
      throw new Error(
        'Required parameter authId was null or undefined when calling retrieveFactorControllerGetFactors.'
      );
    }

    const headers = { ...this.defaultHeaders };

    const accessTokenObservable: Observable<any> = of(null);

    // to determine the Accept header
    const httpHeaderAccepts: string[] = ['application/json'];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = [];
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.get<Factor[]>(
          `${this.basePath}/v1/auth/factor/${encodeURIComponent(
            String(authId)
          )}`,
          {
            withCredentials: this.configuration.withCredentials,
            headers: headers,
          }
        );
      })
    );
  }
  /**
   * generate a sign in with email, email for a user
   * Sends a sign in with email link to the user\&#39;s email address
   * @param sendSignInWithEmailRequest
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public signInWithEmailControllerSendMagicLoginLink(
    sendSignInWithEmailRequest: SendSignInWithEmailRequest
  ): Observable<AxiosResponse<any>>;
  public signInWithEmailControllerSendMagicLoginLink(
    sendSignInWithEmailRequest: SendSignInWithEmailRequest
  ): Observable<any> {
    if (
      sendSignInWithEmailRequest === null ||
      sendSignInWithEmailRequest === undefined
    ) {
      throw new Error(
        'Required parameter sendSignInWithEmailRequest was null or undefined when calling signInWithEmailControllerSendMagicLoginLink.'
      );
    }

    const headers = { ...this.defaultHeaders };

    const accessTokenObservable: Observable<any> = of(null);

    // to determine the Accept header
    const httpHeaderAccepts: string[] = [];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = ['application/json'];
    const httpContentTypeSelected: string | undefined =
      this.configuration.selectHeaderContentType(consumes);
    if (httpContentTypeSelected != undefined) {
      headers['Content-Type'] = httpContentTypeSelected;
    }
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.post<any>(
          `${this.basePath}/v1/auth/sign-in-with-email`,
          sendSignInWithEmailRequest,
          {
            withCredentials: this.configuration.withCredentials,
            headers: headers,
          }
        );
      })
    );
  }
  /**
   * retrieve available telephone codes
   * Retrieves available telephone codes specified by the Firebase configuration
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public telephoneCodesControllerGetTelephoneCodes(): Observable<
    AxiosResponse<any>
  >;
  public telephoneCodesControllerGetTelephoneCodes(): Observable<any> {
    const headers = { ...this.defaultHeaders };

    const accessTokenObservable: Observable<any> = of(null);

    // to determine the Accept header
    const httpHeaderAccepts: string[] = [];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = [];
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.get<any>(
          `${this.basePath}/v1/auth/telephone-codes`,
          {
            withCredentials: this.configuration.withCredentials,
            headers: headers,
          }
        );
      })
    );
  }
  /**
   * changes a user\&#39;s email address
   * Sends a verification email to the user\&#39;s new email address, updating the user\&#39;s email address once the new email address has been verified
   * @param sendVerifyAndChangeEmailRequest
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public verifyAndChangeEmailControllerUpdateEmail(
    sendVerifyAndChangeEmailRequest: SendVerifyAndChangeEmailRequest
  ): Observable<AxiosResponse<any>>;
  public verifyAndChangeEmailControllerUpdateEmail(
    sendVerifyAndChangeEmailRequest: SendVerifyAndChangeEmailRequest
  ): Observable<any> {
    if (
      sendVerifyAndChangeEmailRequest === null ||
      sendVerifyAndChangeEmailRequest === undefined
    ) {
      throw new Error(
        'Required parameter sendVerifyAndChangeEmailRequest was null or undefined when calling verifyAndChangeEmailControllerUpdateEmail.'
      );
    }

    const headers = { ...this.defaultHeaders };

    const accessTokenObservable: Observable<any> = of(null);

    // to determine the Accept header
    const httpHeaderAccepts: string[] = [];
    const httpHeaderAcceptSelected: string | undefined =
      this.configuration.selectHeaderAccept(httpHeaderAccepts);
    if (httpHeaderAcceptSelected != undefined) {
      headers['Accept'] = httpHeaderAcceptSelected;
    }

    // to determine the Content-Type header
    const consumes: string[] = ['application/json'];
    const httpContentTypeSelected: string | undefined =
      this.configuration.selectHeaderContentType(consumes);
    if (httpContentTypeSelected != undefined) {
      headers['Content-Type'] = httpContentTypeSelected;
    }
    return accessTokenObservable.pipe(
      switchMap((accessToken) => {
        if (accessToken) {
          headers['Authorization'] = `Bearer ${accessToken}`;
        }

        return this.httpClient.post<any>(
          `${this.basePath}/v1/auth/verify-and-change-email`,
          sendVerifyAndChangeEmailRequest,
          {
            withCredentials: this.configuration.withCredentials,
            headers: headers,
          }
        );
      })
    );
  }
}
