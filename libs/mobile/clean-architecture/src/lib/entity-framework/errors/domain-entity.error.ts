import { DomainError } from './domain.error';
import { EntityInterface, UnpersistedEntityHelper } from '../';

export class DomainEntityError extends DomainError {
  constructor(
    public readonly dataEntity:
      | EntityInterface<unknown>
      | UnpersistedEntityHelper<EntityInterface<unknown>>,
    reason: string,
  ) {
    const entityName =
      (dataEntity as { constructor?: { name?: string } })?.constructor?.name ||
      'Unknown Entity Type';
    let maybeEntityString = '';

    try {
      maybeEntityString = JSON.stringify(dataEntity);
    } catch {
      maybeEntityString = '(could not stringify entity)';
    }

    const message = [
      `Domain entity error (${entityName})`,
      reason,
      maybeEntityString,
    ].join('\n');

    super(message);
  }
}
